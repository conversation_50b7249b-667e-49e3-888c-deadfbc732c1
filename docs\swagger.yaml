definitions:
  cc.ArticleTipsData:
    properties:
      article_id:
        description: 文章id
        type: integer
      title:
        description: 文章标题
        type: string
    type: object
  cc.ArticleTipsResponse:
    properties:
      code:
        description: 响应码
        type: integer
      data:
        items:
          $ref: '#/definitions/cc.ArticleTipsData'
        type: array
      message:
        description: 返回信息
        type: string
    type: object
  ctc.ArticleListData:
    properties:
      article_id:
        description: 文章id
        type: integer
      article_type:
        description: 文章分类，1-图文，2-视频
        type: integer
      created_at:
        description: 创建时间
        type: string
      doctor_name:
        description: 医生姓名
        type: string
      last_operator:
        description: 最后操作人
        type: string
      last_publish_time:
        description: 最后发布时间
        type: string
      status:
        description: 文章状态，0-未发布，1-已发布，2-下架
        type: string
      title:
        description: 标题
        type: string
      updated_at:
        description: 最后更新时间
        type: string
    type: object
  ctc.ArticleListResponse:
    properties:
      code:
        description: 响应码
        type: integer
      data:
        items:
          $ref: '#/definitions/ctc.ArticleListData'
        type: array
      message:
        description: 返回信息
        type: string
      total_count:
        type: integer
    type: object
  ctc.ArticleMiniFilterResponse:
    properties:
      code:
        description: 200正常，400错误
        type: integer
      message:
        type: string
      tag_groups:
        description: 标签组数据
        items:
          $ref: '#/definitions/ctc.ArticleTagGroup'
        type: array
    type: object
  ctc.ArticleRecommend:
    properties:
      article_type:
        description: 文章类型 1-图文，2-视频
        type: integer
      certificate:
        description: 证书
        type: string
      cover:
        description: 文章封面
        type: string
      doctor_code:
        description: 医生编码
        type: string
      doctor_img:
        description: 医生头像
        type: string
      doctor_level:
        description: 医生职称
        type: string
      doctor_name:
        description: 医生名称
        type: string
      hospital_name:
        description: 医院名称
        type: string
      id:
        description: 文章id
        type: integer
      title:
        description: 文章标题
        type: string
    type: object
  ctc.ArticleRecommendResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/ctc.ArticleRecommend'
        type: array
      message:
        type: string
      total_count:
        description: 总数
        type: integer
    type: object
  ctc.ArticleTagGroup:
    properties:
      name:
        description: 标签组名称
        type: string
      tags:
        description: 多个标签，用逗号分割
        type: string
    type: object
  ctc.Category:
    properties:
      category_name:
        type: string
      create_id:
        type: string
      create_name:
        type: string
      id:
        type: integer
      level:
        type: integer
      parent_id:
        type: integer
      valid:
        type: integer
    type: object
  ctc.CategoryBarResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/ctc.Category'
        type: array
      message:
        type: string
    type: object
  ctc.ContentResponse:
    properties:
      code:
        type: integer
      message:
        type: string
    type: object
  ctc.GetStatisticsResponse:
    properties:
      articleCount:
        description: 文章条数
        type: integer
      categoryCount:
        description: 类目条数
        type: integer
    type: object
  dto.Advertisement:
    properties:
      image:
        description: 图片
        type: string
      path:
        description: 路径
        type: string
    type: object
  dto.AdvertisementDto:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/dto.Advertisement'
        type: object
      message:
        type: string
    type: object
  dto.ArticleDetail:
    properties:
      advertisement:
        $ref: '#/definitions/dto.Advertisement'
        type: object
      article_id:
        description: 文章id
        type: integer
      article_type:
        description: 文章类型：1-图文，2-视频
        type: integer
      category_first:
        description: 一级分类id
        type: integer
      category_second:
        description: 二级分类id
        type: integer
      category_third:
        description: 三级分类id
        type: integer
      content:
        description: 内容
        type: string
      cover_url:
        description: 封面地址
        type: string
      dis_channel:
        description: 分发渠道，1-百度小程序,2-阿闻小程序，4-阿闻app
        type: integer
      dis_channel_arr:
        items:
          type: integer
        type: array
      doctor_cert_no:
        description: 执业证书编号
        type: string
      doctor_code:
        description: 医生code，接口中doctor_code
        type: string
      doctor_img:
        description: 医生头像
        type: string
      doctor_level:
        description: 医生称号（职级、岗位）
        type: string
      doctor_name:
        description: 医生名称
        type: string
      hospital_name:
        description: 医院名称
        type: string
      is_show_ads:
        description: 是否显示广告，1-显示，0-不显示
        type: integer
      online_ask_url:
        description: 在线问诊入口图片地址
        type: string
      tag_json:
        description: 标签集合
        type: string
      template_id:
        description: 模板id
        type: integer
      title:
        description: 标题
        type: string
      updated_at:
        description: 最后更新时间
        type: string
      video_url:
        description: 视频地址
        type: string
    type: object
  dto.ArticleDetailDto:
    properties:
      code:
        type: integer
      detail:
        $ref: '#/definitions/dto.ArticleDetail'
        type: object
      message:
        type: string
    type: object
  dto.GetStatisticsDto:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/ctc.GetStatisticsResponse'
        type: object
      message:
        type: string
    type: object
  dto.NftBuyDto:
    properties:
      price:
        type: number
      publish_identification:
        type: string
      sell_type:
        type: integer
      series_id:
        type: string
    required:
    - publish_identification
    - series_id
    type: object
  dto.NftFlagDto:
    properties:
      desc:
        description: 藏品描述
        type: string
      detail:
        description: 详情图片
        type: string
      flag:
        description: 是否购买唯一标识,0是未购买,1是已购买
        type: integer
      image_url:
        description: 藏品封面图
        type: string
      name:
        description: 藏品名称
        type: string
      price:
        description: 藏品价格
        type: number
      publish_identification:
        description: 发行人唯一标识
        type: string
      series_id:
        description: 藏品系列
        type: string
    type: object
  dto.NftPayDto:
    properties:
      order_sn:
        description: 数字藏品订单号
        type: string
    required:
    - order_sn
    type: object
  dto.RespDto:
    properties:
      code:
        type: integer
      data:
        type: object
      message:
        type: string
    type: object
  dto.UserRegisterDto:
    properties:
      id_card:
        type: string
      mobile:
        type: string
      openid:
        type: string
      person_name:
        type: string
    type: object
  models.AddBoehringereRecord:
    properties:
      type:
        description: 类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
        type: integer
      type_child:
        description: 类型子级（0为标题，1为内容一，以此类推）
        type: integer
    type: object
  models.AddVisitRecord:
    properties:
      channel:
        description: 访问渠道 1：百度小程序 2：阿闻小程序  3：安卓 4：IOS
        type: integer
      content_id:
        description: 文章id
        type: integer
      type:
        description: 类型 1：文章访问 2：视频播放
        type: integer
      user_no:
        description: 用户编号或者openId
        type: string
    type: object
  models.BiliAdConversion:
    properties:
      client_ip:
        description: 设备的联网公网IP
        type: string
      conv_type:
        description: 转化类型，APP_FIRST_ACTIVE：新用户下载应用后首次打开应用
        type: string
      idfa:
        description: iOS广告标识
        type: string
      imei:
        description: 安卓用户终端的IMEI
        type: string
      mac:
        description: 用户终端的eth0接口的MAC地址
        type: string
      model:
        description: 用户手机型号
        type: string
      nonce:
        description: 不重复的随机数
        type: string
      oaid:
        description: 安卓终端设备ID
        type: string
      os:
        description: 客户端操作系统：0 Andriod、1 IOS、2 WindowsPhone、3 其他
        type: integer
      sign:
        description: 请求签名，md5(arg1Value1arg2value2...)
        type: string
      timestamp:
        description: 请求时间戳
        type: integer
      ua:
        description: 数据上报终端设备的User Agent
        type: string
    type: object
host: localhost:7086
info:
  contact: {}
  description: 这里是描述
  license: {}
  title: 项目接口文档
  version: "1.0"
paths:
  /content-api/advertisement/get:
    get:
      consumes:
      - application/json
      parameters:
      - description: 0：默认是商详广告，1：百度勃林格
        in: body
        name: type
        required: true
        schema:
          type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.AdvertisementDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.AdvertisementDto'
      summary: 获取广告
      tags:
      - 内容中心
  /content-api/article/category_bar:
    get:
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - description: 0：默认一级分类，1：二级分类
        in: body
        name: type
        required: true
        schema:
          type: integer
      - description: 标签过滤 json字符串 [{'name':'年龄','tags':'幼年'}]
        in: body
        name: tags
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.CategoryBarResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.CategoryBarResponse'
      summary: 获取类目栏填充类目
      tags:
      - 内容中心
  /content-api/article/detail:
    get:
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - description: 文章id
        in: body
        name: article_id
        required: true
        schema:
          type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.ArticleDetailDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.ArticleDetailDto'
      summary: 文章详情
      tags:
      - 内容中心
  /content-api/article/list:
    get:
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - description: 一级分类id
        in: body
        name: category_first
        schema:
          type: integer
      - description: 二级分类id
        in: body
        name: category_second
        schema:
          type: integer
      - description: 0全部 1图文 2视频
        in: body
        name: articleType
        schema:
          type: integer
      - description: 随机值，0为不随机取值
        in: body
        name: randNum
        schema:
          type: integer
      - description: 分发渠道，1-百度小程序,2-阿闻小程序,4-阿闻APP，多渠道1+2+4，查询位运算
        in: body
        name: disChannel
        schema:
          type: integer
      - description: 标签过滤 json字符串 [{'name':'年龄','tags':'幼年'}]
        in: body
        name: tags
        schema:
          type: string
      - description: 页码，默认1
        in: body
        name: page_index
        required: true
        schema:
          type: integer
      - description: 每页条数，默认20
        in: body
        name: page_size
        required: true
        schema:
          type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.ArticleListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.ArticleListResponse'
      summary: 文章列表
      tags:
      - 内容中心
  /content-api/article/mini-filter:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.ArticleMiniFilterResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.ArticleMiniFilterResponse'
      summary: 小程序文章列表过滤数据
      tags:
      - 内容中心
  /content-api/article/recommend:
    get:
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - description: 文章id
        in: body
        name: article_id
        required: true
        schema:
          type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.ArticleRecommendResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.ArticleRecommendResponse'
      summary: 延伸阅读
      tags:
      - 内容中心
  /content-api/article/statistics:
    get:
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.GetStatisticsDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.GetStatisticsDto'
      summary: 获取二级类目和文章的条数
      tags:
      - 内容中心
  /content-api/article/tips:
    get:
      consumes:
      - application/x-www-form-urlencoded
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/cc.ArticleTipsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/cc.ArticleTipsResponse'
      summary: 文章小贴士
      tags:
      - 内容中心
  /content-api/biliad/callback:
    get:
      consumes:
      - application/json
      parameters:
      - description: 追踪id
        in: query
        name: track_id
        required: true
        type: string
      - description: B站账户ID
        in: query
        name: account_id
        required: true
        type: string
      - description: B站计划ID
        in: query
        name: campaign_id
        required: true
        type: string
      - description: B站单元ID
        in: query
        name: unit_id
        required: true
        type: string
      - description: B站创意ID
        in: query
        name: creative_id
        required: true
        type: string
      - description: 客户端操作系统：0 Andriod、1 IOS、2 WindowsPhone、3 其他
        in: query
        name: os
        required: true
        type: string
      - description: 用户终端的IMEI的md5值
        in: query
        name: imei
        type: string
      - description: 回调地址，urlencode值
        in: query
        name: callback_url
        type: string
      - description: 用户终端的eth0接口的MAC地址,大写MAC的md5值
        in: query
        name: mac1
        type: string
      - description: iOS idfa广告标识，适用于iOS6及以上，取md5值
        in: query
        name: idfa
        type: string
      - description: 中广协CAID
        in: query
        name: caid
        type: string
      - description: 安卓广告标识 Android Advertising ID
        in: query
        name: aaid
        type: string
      - description: 用户终端安卓id，取md5值
        in: query
        name: android_id
        type: string
      - description: 安卓设备ID，取md5值
        in: query
        name: oaid
        type: string
      - description: 投放系统服务器观察到的用户远程IP
        in: query
        name: ip
        type: string
      - description: 数据上报终端设备的User Agent，需escape转义
        in: query
        name: ua
        type: string
      - description: 手机型号，仅支持iphone
        in: query
        name: model
        type: string
      - description: 客户端触发监测的时间，毫秒
        in: query
        name: ts
        type: string
      - description: 店铺ID
        in: query
        name: shop_id
        type: string
      - description: 视频UP主MID
        in: query
        name: up_mid
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
      summary: B站广告推广回调地址
      tags:
      - 内容中心
  /content-api/biliad/check-conv:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/models.BiliAdConversion'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
      summary: 校验B站广告归因
      tags:
      - 内容中心
  /content-api/census/boehringere:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/models.AddBoehringereRecord'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
      summary: 添加勃林格访问记录
      tags:
      - 内容中心
  /content-api/census/visit:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/models.AddVisitRecord'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/ctc.ContentResponse'
      summary: 添加访问记录
      tags:
      - 内容中心
  /content-api/digital/auth_openid:
    get:
      consumes:
      - application/json
      parameters:
      - description: code码
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.RespDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.RespDto'
      summary: 获取UserAuthOpenid
      tags:
      - 数字藏品
  /content-api/digital/nft_buy:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/dto.NftBuyDto'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.RespDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.RespDto'
      summary: 数字藏品购买
      tags:
      - 数字藏品
  /content-api/digital/nft_flat_detail:
    get:
      consumes:
      - application/json
      parameters:
      - description: 系列id
        in: path
        name: series_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.NftFlagDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.NftFlagDto'
      summary: 数字藏品详情展示
      tags:
      - 数字藏品
  /content-api/digital/nft_flat_list:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/dto.NftFlagDto'
            type: array
        "400":
          description: Bad Request
          schema:
            items:
              $ref: '#/definitions/dto.NftFlagDto'
            type: array
      summary: 数字藏品列表展示
      tags:
      - 数字藏品
  /content-api/digital/nft_pay:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/dto.NftPayDto'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.RespDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.RespDto'
      summary: 数字藏品支付
      tags:
      - 数字藏品
  /content-api/digital/register:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/dto.UserRegisterDto'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.RespDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.RespDto'
      summary: 用户实名注册
      tags:
      - 数字藏品
  /content-api/digital/user_code:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.RespDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.RespDto'
      summary: 获取access_code链接
      tags:
      - 数字藏品
  /content-api/digital/user_nft_list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 第几页,不传默认为1
        in: path
        name: page
        type: integer
      - description: 每页数量,不传默认为10
        in: path
        name: size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.RespDto'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dto.RespDto'
      summary: 获取nft数字藏品列表
      tags:
      - 数字藏品
swagger: "2.0"
