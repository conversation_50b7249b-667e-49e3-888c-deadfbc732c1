package utils

import (
	"github.com/labstack/echo/v4"
	"net/http"
)

const (
	ResultFail = 400
	ResultOk   = 200
)

const (
	ErrorMsgSuccess = "OK"
	ErrorMsgFail    = "ERROR"
)

type Result struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

//Ok 成功返回
func Ok(c echo.Context, data interface{}, errs ...string) error {
	return c.JSON(http.StatusOK, ok(data, errs...))
}

//Fail 失败返回
func Fail(c echo.Context, errs ...string) error {
	return c.JSON(http.StatusBadRequest, fail(errs...))
}

func fail(errs ...string) *Result {
	ret := &Result{
		Code:    ResultFail,
		Message: ErrorMsgFail,
	}
	for i, err := range errs {
		if i == 0 { // error_msg
			ret.Message = err
		} else {
			break
		}
	}
	return ret
}

func ok(data interface{}, errs ...string) *Result {
	ret := &Result{
		Code:    ResultOk,
		Message: ErrorMsgSuccess,
	}
	ret.Data = data
	for i, err := range errs {
		if i == 0 { // error_msg
			ret.Message = err
		} else {
			break
		}
	}
	return ret
}
