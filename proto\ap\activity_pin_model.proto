syntax = "proto3";

package ap;

//------------------拼团模块------------------------------------------

//通用返回
message BaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message CancelPinOrderResponse{
     //状态码
     int32 code = 1;
     //消息
     string message = 2;
     //错误信息
     string error = 3;
      //下单时间(13位，毫秒)
     int64 create_time=4;
}

//订单支付通知
message OrderPayNotifyRequest {
  //订单号
  string order_sn = 1;
  //支付单号
  string pay_sn = 2;
  //支付类型1支付宝  2微信 3美团 4其他
  int32 pay_mode = 3;
  //支付时间
  string pay_time = 4;
  //实际支付金额
  int32 pay_amount = 5;
}

message GetGroupListRequest{
  //用户小程序ID
  string open_id = 1;
  //用户ID
  string user_id = 2;
  //商品SKUid
  string sku_id = 3;
  //当前页码
  int32 page_index = 4;
  //每页行数
  int32 page_size = 5;
}

message GetGroupListResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  int32 total_count = 4;
  //拼团明细
  repeated GroupDetail data = 5;
}

message GetGroupDetailRequest{
  //拼团订单号
  string pin_order_sn = 1;
  // 用户ID
  string user_id = 2;
  // 是否是分享界面过来的
  int32 is_share = 3;
}
message GetGroupDetailResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //拼团明细
  GroupDetail data = 4;
}
message GroupDetail{
  //拼团订单号
  string pin_order_sn = 1;
  //商品skuid
  string sku_id = 2;
  //拼团状态 0已取消 10未支付 20拼团进行中 30拼团成功 40拼团失败
  int32 status = 3;
  //开始时间
  string start_time = 4;
  //结束时间
  string end_time = 5;
  //创建时间
  string create_time = 6;
  //参团人
  repeated GroupMembers group_members = 7;
  //最大参与人数
  int32 max_participant_number = 8;
  //已参与人数
  int32 participant_number = 9;
  // 商品名称
  string product_name = 10;
  // 商品图片
  string product_image = 11;
  // 规格名称
  string spec_name = 12;
  // 拼团价
  int64 pin_price = 13;
  // 商品原价
  int64 marking_price = 14;
  // 是否团长 1是 0 否
  int32 is_pin_head = 15;
  // 用户名
  string open_name = 16;
  // 头像
  string portrait = 17;
  // 团长订单号
  string pin_head_order_sn = 18;
  // 团长头像
  string pin_head_portrait = 19;
  // 是否参团 0 否 1 是
  int32  is_add_group = 20;
  //是否虚拟订单 0否 1是
  int32 is_virtual = 21;
  // 拼团活动ID
  int32 group_buy_id = 22;
  // 是否发券 0默认 1已发优惠券
  int32 is_coupon = 23;
  // 当前时间
  string now_time = 24;
  // 分享卡片图片地址
  string share_img_url = 25;
  // 拼团成功送的优惠券ID
  string success_coupon = 26;
  //团状态：0拼主已创建但未支付 10拼团进行中 20 拼团成功 30 拼团失败
  int32 group_status = 27;
  //活动id
  int32 pin_group_id = 28;
  //数量
  int32 number = 29;

}
message GroupMembers{
  //用户小程序ID
  string open_id = 1;
  //用户小程序名称
  string open_name = 2;
  //用户ID
  string user_id = 3;
  //头像
  string portrait = 4;
  // 创建时间（参与时间）
  string create_time = 5;
}
message GetGroupParticipantRequest{
  //拼团活动商品skuid
  string sku_id = 1;
  //拼团活动id
  int64 group_id = 2;
  //当前页码
  int32 page_index = 4;
  //每页行数
  int32 page_size = 5;
}

message GetGroupParticipantResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  int32 total_count = 4;

  repeated Participant data = 5;
}

message Participant{
  //用户小程序ID
  string open_id = 1;
  //用户小程序名称
  string open_name = 2;
  //用户ID
  string user_id = 3;
  //头像
  string portrait = 4;
  //类型 1发起拼团 2参团成功 3拼团成功
  int32 type = 5;
  //创建时间
  string create_time = 6;
}

message CreateGroupOrderRequest{
  //是否团长 1是 0 否
  int32 is_pin_head = 1;
  //参团需要带上团长订单号
  string pin_head_order_sn = 2;
  //拼团活动id
  int64 group_buy_id = 3;
  //最大参与人数
  int32 max_participant_number = 4;
  //参与人数
  int32 participant_number = 5;
  //渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
  int32 channel_id = 6;
  //头像
  string portrait = 7;
  //小程序的用户id
  string open_id = 8;
  //用户昵称
  string open_name = 9;
  //用户id
  string user_id = 10;
  //拼团商品sku_id
  string sku_id = 11;
  //开始时间
  string start_time = 12;
  //结束时间
  string end_time = 13;
  //业务端json
  string business_json = 14;
  //提交到订单中心json
  string submit_json = 15;
  //总金额
  int32 total_price = 16;
  //支付金额
  int32 pay_price = 17;
  //订单状态：0 已取消 10未支付 20 拼团进行中 30拼团成功 40拼团失败
  int32 status = 18;
  //拼团商品列表
  repeated GroupOrderProduct product_list = 19;
  //店铺财务编码
  string shop_id = 20;
  //是否虚拟订单 0否 1是
  int32 is_virtual = 21;
  //交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
  string order_pay_type = 22;
}
message GroupOrderProduct{
  //商品SKUID
  string sku_id = 1;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 2;
  //父商品skuid
  string parent_sku_id = 3;
  //商品名称
  string product_name = 4;
  //商品图片
  string product_image = 5;
  //规格值
  string spec_name = 6;
  //商品原单价
  int32 marking_price = 7;
  //商品拼团单价
  int32 pin_price = 8;
  //商品均摊后实际支付单价
  int32 pay_price = 9;
  //数量
  int32 number = 10;
  //单人购买的最大数量
  int32 max_number = 11;
  //子商品列表
  repeated GroupOrderProduct childProduct = 12;
}

message CreateGroupOrderResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //拼团订单号
  string pin_order_sn = 4;
}
message GroupOrderPayRequest{
  //付款码
  string bar_code = 1;
  //订单号
  string pin_order_sn = 2;
  //微信用户标识 JSAPI 支付时必传
  string open_id = 3;
}
message CancelGroupOrderRequest{
  //拼团订单号
  string pin_order_sn = 1;
  //原因备注
  string reason = 2;
}
message SetOrderSubscribeMessageRequest{
  //拼团订单号
  string pin_order_sn = 1;
  //小程序的用户id
  string open_id = 2;
  //用户id
  string user_id = 3;
  //消息类型：1拼团成功 2拼团失败 3支付成功 4超时未支付取消 5退款成功
  int32 type = 4;
}


//获取拼团商品的订单统计
message GetPinGroupProductOrderStaticsRequest {
  //拼团订单id
  int32 groupBuyId = 1;
  //拼团商品的skuId
  int32 skuId = 2;
  //渠道
  int32 channelId = 3;
  PinGroupProductOrderStatics fields = 4;
}

//获取拼团商品的订单统计
message GetPinGroupProductOrderStaticsResponse {
  //成功的团里的去重人数
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  PinGroupProductOrderStatics data = 4;
}
//订单统计字段
message PinGroupProductOrderStatics{
  //总订单数记录数 包括所有的数据库记录 0或1 0表示统计 1表示不统计 返回时表示该统计的结果值
  int32 totalOrderCount = 1;
  //成功的团里的去重人数 请求是传 0或1 0表示统计 1表示不统计 返回时表示该统计的结果值
  int32 successDistinctUserCount = 2;
  //成功的团里的真人订单数（去掉虚拟的模拟的订单） 请求是传 0 或1 0表示统计 1表示不统计 返回时表示该统计的结果值
  int32 successRealOrderCount = 3;
}

message PinOrderPayRequest{
  //订单id
  string pin_order_sn = 1;
  //微信用户标识 JSAPI 支付时必传
  string open_id = 2;
  //支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
  int32 trans_type = 3;
}
message GroupOrderListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated PinGroupOrderListInfo details = 4;
  // 总数
  int32 total_count = 5;
}
message PinGroupOrderListInfo {
  // 订单索引ID
  int64 id = 1;
  // 订单编号
  string pin_order_sn = 2;
  // 订单状态：0已取消 10未支付 20拼团进行中 30拼团成功 40拼团失败
  int32 status = 3;
  // 开始时间
  string start_time = 4;
  // 结束时间
  string end_time = 5;
  // 需要人数
  int32 need_number = 6;
  // 是否团长 1是 0 否
  int32 is_pin_head = 7;
  //最大参与人数
  int32 max_participant_number = 8;
  //参与人数
  int32 participant_number = 9;
  // 商品skuid
  string sku_id = 10;
  // 商品名称
  string product_name = 11;
  // 商品图片
  string product_image = 12;
  // 规格名称
  string spec_name = 13;
  // 拼团价
  int64 pin_price = 14;
  // 商品原价
  int64 marking_price = 15;
  //结束时间 时间戳
  int64 etime = 16;
  // 当前时间
  string now_time = 17;
  // 创建时间
  string create_time = 18;
  // 支付结束时间
  string pay_end_time = 19;
  // 是否虚拟订单 0 否 1 是
  int32 is_virtual = 20;
  //团状态：0拼主已创建但未支付 10拼团进行中 20 拼团成功 30 拼团失败
  int32 group_status = 21;
  //数量
  int32 number = 22;
  //拼团父订单号
  string parent_pin_order_sn = 23;
}
message GetCanParticipantOrderListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated CanParticipantOrder data = 4;
  // 总数
  int32 total_count = 5;
}
message CanParticipantOrder {
  // 用户昵称
  string open_name = 1;
  // 头像
  string portrait = 2;
  // 最大参与人数
  int32 max_participant_number = 3;
  // 参与人数
  int32 participant_number = 4;
  // 开始时间
  string start_time = 5;
  // 结束时间
  string end_time = 6;
  // 拼团商品SKUID
  string pin_sku_id = 7;
  // 订单编号
  string pin_order_sn = 8;
  // 拼团订单父订单号
  string parent_pin_order_sn = 9;
  // 拼团活动id
  int32 group_buy_id = 10;
}
message NewSubscribeMessageRequest {
  // 拼团订单ID
  string order_sn = 1;
  // 小程序用户ID
  string open_id = 2;
  // 用户ID
  string user_id = 3;
  // 微信订阅消息
  repeated WxSubscribeMessage wx_info = 4;
}
message WxSubscribeMessage {
  // 微信模板ID
  string template_id = 1;
  // 消息类型：1拼团成功 2拼团失败 3支付成功 4超时未支付取消 5退款成功
  int32 type = 2;
}
message SubscribeMessageListRequest {
  // 拼团订单ID
  string order_sn = 1;
  // 小程序用户ID
  string open_id = 2;
  //当前页码
  int32 page_index = 3;
  //每页行数
  int32 page_size = 4;
  // 微信模板ID,多个英文逗号隔开
  string template_id = 5;
}
message SubscribeMessageListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated OrderSubscribeMessage data = 4;
}
message OrderSubscribeMessage {
  // 索引ID
  int32 id = 1;
  // 拼团订单ID
  string order_sn = 2;
  // 微信模板ID
  string template_id = 3;
  // 消息类型：1拼团成功 2拼团失败 3订单状态 4售后单状态 5退款成功 6售后单审核不通过
  int32 type = 4;
  string create_time = 5;
}
message PinOrderRefundRequest{
  //订单id
  string pin_order_sn = 1;
}

message RefundNotifyRequest{
  //拼团单号
  string pin_order_sn = 1;
  //退款流水号
  string transaction_no = 2;
  //退款金额（分）
  int32 refund_amount = 3;
  string res_code = 4;
  string res_message = 5;
}

message UnifiedOrderReturnData{
  //微信appid
  string appId = 1;
  //随机字符串，不长于 32位
  string nonceStr = 2;
  //订单详情扩展字符串
  string package = 3;
  //签名
  string paySign = 4;
  //签名方式
  string signType = 5;
  //时间戳
  string timeStamp = 6;
}

message UnifiedOrderResponse{
  int32 code = 1;
  string message = 2;
  data details = 3;
  //下单时间(13位，毫秒)
  int64 create_time=4;
  message data{
    //当支付方式为wx_jsapi返回
    UnifiedOrderReturnData wx_jsapi = 1;
    string order_id = 2;
    //当支付方式为WX_NATIVE返回
    UnifiedOrderWxNative wx_native = 3;
    //当支付方式为WX_JSAPP返回
    UnifiedOrderWxJsApp wx_js_app=4;
    //当支付方式是网银
    string bank = 5;
    // 支付宝小程序交易单号
    string aliTradeNo = 6;
    // 百度支付订单信息
    UnifiedOrderBdPay bd_order_info = 7;
  }
  //支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
  int32 pay_type=5;
}
message  UnifiedOrderWxJsApp{
    //transType 为 WX_JSAPP/AL_JSAPP 时返回
    string jsAppId = 1;
    //transType 为 WX_JSAPP/AL_JSAPP 时返回
    string jsAppUrl = 2;
}

message UnifiedOrderWxNative{
  //支付链接
  string payUrl = 1;
}

// 百度支付信息
message UnifiedOrderBdPay {
  string dealId = 1;       // 跳转百度收银台支付必带参数之一，是百度收银台的财务结算凭证
  string appKey = 2;          // 支付能力开通后分配的支付 appKey，用以表示应用身份的唯一 ID
  string totalAmount = 3;     // 订单金额（单位：人民币分）。注：小程序测试包测试金额不可超过 1000 分
  string tpOrderId = 4;       // 小程序开发者系统创建的唯一订单 ID ，当支付状态发生变化时，会通过此订单 ID 通知开发者。
  string notifyUrl = 5;       // 通知开发者支付状态的回调地址，必须是合法的 URL ，与开发者平台填写的支付回调地址作用一致，未填写的以平台回调地址为准
  string dealTitle = 6;       // 订单的名称
  string signFieldsRange = 7; // 用于区分验签字段范围，signFieldsRange 的值：0：原验签字段 appKey+dealId+tpOrderId；1：包含 totalAmount 的验签，验签字段包括appKey+dealId+tpOrderId+totalAmount。固定值为 1
  string rsaSign = 8;         // 对appKey+dealId+totalAmount+tpOrderId进行 RSA 加密后的签名，防止订单被伪造
  UnifiedOrderBdPayBizInfo bizInfo  = 9;
}
message UnifiedOrderBdPayBizInfo {
  BizInfoTpData tpData = 1;
}
message BizInfoTpData {
  string dealId = 1;
  string appKey = 2;
  string totalAmount = 3;
  string tpOrderId = 4;
}

message StopGroupRequest{
  //拼团活动ID
  int32 group_id = 1;
  //商品skuid
  int32 sku_id = 2;
  //是否模拟成团 1 是 0 否
  int32 is_mock = 3;

}
//某个活动下某个商品被用户购买了多少件请求参数
message GetUserOrderCountRequest{
  //用户id userId
  string userId = 1;
  //用户 openId
  string openId = 2;
  //拼团活动id
  int32 gid = 3;
  //商品skuId
  int32 skuId = 4;
  //渠道id 1阿闻本地 5阿闻商城
  int32 channelId = 5;
}

//某个活动下某个商品被用户购买了多少件返回参数
message GetUserOrderCountResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //买了多少件
  int32  data = 4;
}

//获取拼团信息请求参数
message GetPinOrderMainInfoRequest{
  //团号
  string pinOrderSn = 1;
}

//获取拼团信息返回参数
message GetPinOrderMainInfoResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //买了多少件
  PinOrderMainData  data = 4;
}

//拼团信息，当前只返回了两个字段 后续如果有需要 往这里面加字段
message PinOrderMainData {
  //拼团活动ID
  int32 groupBuyId = 1;
  //拼团结束时间 = 拼团开始时间+团活动的有效时间
  string endTime = 2;
  //拼团的skuId
  int32 skuId = 3;
}