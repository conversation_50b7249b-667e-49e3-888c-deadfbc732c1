package models

// b站广告回传数据
type BiliAdConversion struct {
	// 转化类型，APP_FIRST_ACTIVE：新用户下载应用后首次打开应用
	ConvType string `json:"conv_type"`
	// 客户端操作系统：0 Andriod、1 IOS、2 WindowsPhone、3 其他
	Os int64 `json:"os"`
	// 安卓用户终端的IMEI
	Imei string `json:"imei"`
	// iOS广告标识
	Idfa string `json:"idfa"`
	// 安卓终端设备ID
	Oaid string `json:"oaid"`
	// 用户终端的eth0接口的MAC地址
	Mac string `json:"mac"`
	// 设备的联网公网IP
	ClientIp string `json:"client_ip"`
	// 用户手机型号
	Model string `json:"model"`
	// 数据上报终端设备的User Agent
	Ua string `json:"ua"`
	// 请求签名，md5(arg1Value1arg2value2...)
	Sign string `json:"sign"`
	// 请求时间戳
	Timestamp int64 `json:"timestamp"`
	// 不重复的随机数
	Nonce string `json:"nonce"`
}
