package controller

import (
	"_/dto"
	"_/proto/ctc"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

//NftCreate 创建数字藏品
func NftCreate(c echo.Context) error {
	createDto := new(dto.NftCreateDto)
	if err := c.Bind(&createDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	// rpc请求
	in := new(ctc.NftCreateRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.Name = createDto.Name
	in.ImageUrl = createDto.ImageUrl
	in.SeriesId = createDto.SeriesId
	in.UserIdentification = createDto.UserIdentification
	in.Detail = createDto.Detail
	in.Price = float32(createDto.Price)
	in.Desc = createDto.Desc
	out, err := client.DigitRPC.NftCreate(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	return utils.Ok(c, out)
}

//NftUpdate 更新数字藏品
func NftUpdate(c echo.Context) error {
	updateDto := new(dto.NftUpdateDto)
	if err := c.Bind(&updateDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	// rpc请求
	in := new(ctc.NftUpdateRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.Name = updateDto.Name
	in.ImageUrl = updateDto.ImageUrl
	in.SeriesId = updateDto.SeriesId
	in.UserIdentification = updateDto.UserIdentification
	in.Detail = updateDto.Detail
	in.Price = float32(updateDto.Price)
	in.Desc = updateDto.Desc
	in.Id = int32(updateDto.Id)
	in.Status = int32(updateDto.Status)
	out, err := client.DigitRPC.NftUpdate(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	return utils.Ok(c, out)
}

//NftDelete 删除数字藏品
func NftDelete(c echo.Context) error {
	id := c.Param("id")
	// rpc请求
	in := new(ctc.NftDeleteRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.Id = cast.ToInt32(id)
	out, err := client.DigitRPC.NftDelete(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	return utils.Ok(c, out)
}

// NftFlatList 数字藏品列表展示
// @Summary 数字藏品列表展示
// @Tags 数字藏品
// @Accept json
// @Produce json
// @Success 200 {array} dto.NftFlagDto
// @Failure 400 {array} dto.NftFlagDto
// @Router /content-api/digital/nft_flat_list [GET]
func NftFlatList(c echo.Context) error {
	// rpc请求
	in := new(ctc.NftRuiPengListRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	out, err := client.DigitRPC.NftRuiPengList(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	list := make([]*dto.NftFlagDto, 0, len(out.Data))
	for _, d := range out.Data {
		list = append(list, &dto.NftFlagDto{
			Name:                  d.Name,
			ImageUrl:              d.ImageUrl,
			Desc:                  d.Desc,
			SeriesId:              d.SeriesId,
			Price:                 float64(d.Price),
			Detail:                d.Detail,
			PublishIdentification: d.PublishIdentification,
		})
	}
	return utils.Ok(c, list)
}

// NftFlatDetail 数字藏品详情
// @Summary 数字藏品详情展示
// @Tags 数字藏品
// @Accept json
// @Produce json
// @param series_id path string true "系列id"
// @Success 200 {object} dto.NftFlagDto
// @Failure 400 {object} dto.NftFlagDto
// @Router /content-api/digital/nft_flat_detail [GET]
func NftFlatDetail(c echo.Context) error {
	seriesId := c.QueryParam("series_id")
	if seriesId == "" {
		return utils.Fail(c, "请传入系列id")
	}
	userIdentification := c.Get("user_id").(string)
	// rpc请求
	in := new(ctc.NftRuiPengDetailRequest)
	in.SeriesId = seriesId
	in.UserIdentification = userIdentification
	client := ctc.GetContentCenterClient()
	defer client.Close()
	out, err := client.DigitRPC.NftRuiPengDetail(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	info := out.Data
	nft := dto.NftFlagDto{
		Name:                  info.Name,
		Desc:                  info.Desc,
		SeriesId:              info.SeriesId,
		Price:                 float64(info.Price),
		PublishIdentification: info.PublishIdentification,
		Detail:                info.Detail,
		Flag:                  info.Flag,
	}
	return utils.Ok(c, nft)
}

// AccessCode 获取accessCode链接
// @Summary 获取access_code链接
// @Tags 数字藏品
// @Accept json
// @Produce json
// @Success 200 {object} dto.RespDto
// @Failure 400 {object} dto.RespDto
// @Router /content-api/digital/user_code [GET]
func AccessCode(c echo.Context) error {
	userIdentification := cast.ToString(c.Get("user_id"))
	// rpc请求
	in := new(ctc.UserOpenidRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.UserIdentification = userIdentification
	out, err := client.DigitRPC.UserOpenid(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	return utils.Ok(c, out.Url)
}

// UserAuthOpenid 获取auth_openid
// @Summary 获取UserAuthOpenid
// @Tags 数字藏品
// @Accept json
// @param code path string true "code码"
// @Produce json
// @Success 200 {object} dto.RespDto
// @Failure 400 {object} dto.RespDto
// @Router /content-api/digital/auth_openid [GET]
func UserAuthOpenid(c echo.Context) error {
	code := c.QueryParam("code")
	in := new(ctc.UserAuthOpenidRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.Code = code
	out, err := client.DigitRPC.UserAuthOpenid(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	return utils.Ok(c, out.Openid)
}

// UserRegister 用户实名注册,返回登录token
// @Summary 用户实名注册
// @Tags 数字藏品
// @Accept json
// @Produce json
// @Param model body dto.UserRegisterDto true " "
// @Success 200 {object} dto.RespDto
// @Failure 400 {object} dto.RespDto
// @Router /content-api/digital/register [POST]
func UserRegister(c echo.Context) error {
	registerDto := new(dto.UserRegisterDto)
	if err := c.Bind(&registerDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	if registerDto.PersonName == "" {
		return utils.Fail(c, "请输入姓名")
	}
	if registerDto.Mobile == "" {
		return utils.Fail(c, "请输入手机号")
	}
	if registerDto.IdCard == "" {
		return utils.Fail(c, "请输入身份证")
	}
	// rpc请求
	in := new(ctc.RealRegisterRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.PersonName = registerDto.PersonName
	in.Mobile = registerDto.Mobile
	in.IdCard = registerDto.IdCard
	in.Openid = registerDto.Openid
	out, err := client.DigitRPC.RealRegister(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}

	//创建用户jwt
	token, err := utils.CreateJwtToken(in.Mobile, out.UserIdentification, in.PersonName)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	res := map[string]interface{}{
		"token": token,
	}
	return utils.Ok(c, res)
}

//DigitalImageUpload 数字藏品素材上传
func DigitalImageUpload(c echo.Context) error {
	filePath := c.QueryParam("file_path")
	userIdentification := c.Get("user_id").(string)
	in := new(ctc.DigitalImageUploadRequest)
	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.FilePath = filePath
	in.UserIdentification = userIdentification

	out, err := client.DigitRPC.DigitalImageUpload(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	res := map[string]interface{}{
		"image_url": out.ImageUrl,
	}
	return utils.Ok(c, res)
}

//NftClaim 声明数字藏品系列
func NftClaim(c echo.Context) error {
	in := new(ctc.NftSeriesClaimRequest)
	claimDto := new(dto.NftClaimDto)
	if err := c.Bind(&claimDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	userIdentification := c.Get("user_id").(string)
	in.SeriesName = claimDto.SeriesName
	in.CoverUrl = claimDto.CoverUrl
	in.Desc = claimDto.Desc
	in.TotalCount = int32(claimDto.TotalCount)
	in.UserIdentification = userIdentification
	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	out, err := client.DigitRPC.NftSeriesClaim(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	res := map[string]interface{}{
		"taskId": out.TaskId,
	}
	return utils.Ok(c, res)
}

//NftPublish 发行数字藏品
func NftPublish(c echo.Context) error {
	in := new(ctc.NftPublishDigitalRequest)
	publishDto := new(dto.NftPublishDto)
	if err := c.Bind(&publishDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	userIdentification := c.Get("user_id").(string)
	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	//绑定数据
	in.Author = publishDto.Author
	in.Name = publishDto.Name
	in.Url = publishDto.Url
	in.DisplayUrl = publishDto.DisplayUrl
	in.Desc = publishDto.Desc
	in.Flag = publishDto.Flag
	in.UserIdentification = userIdentification
	in.PublishCount = int32(publishDto.PublishCount)
	in.SeriesId = publishDto.SeriesId
	in.SeriesBeginIndex = int32(publishDto.SeriesBeginIndex)
	in.SellStatus = int32(publishDto.SellStatus)
	in.SellCount = int32(publishDto.SellCount)
	in.PackageType = publishDto.PackageType
	out, err := client.DigitRPC.NftPublishDigital(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	res := map[string]interface{}{
		"taskId": out.TaskId,
	}
	return utils.Ok(c, res)
}

// UserNftList 获取nft列表
// @Summary 获取nft数字藏品列表
// @Tags 数字藏品
// @Accept json
// @Produce json
// @param page path integer false "第几页,不传默认为1"
// @param size path integer false "每页数量,不传默认为10"
// @Success 200 {object} dto.RespDto
// @Failure 400 {object} dto.RespDto
// @Router /content-api/digital/user_nft_list [GET]
func UserNftList(c echo.Context) error {
	in := new(ctc.NftSeriesListRequest)
	userIdentification := c.Get("user_id")
	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	page := c.QueryParam("page")
	size := c.QueryParam("size")
	if page == "" {
		page = "1"
	}
	if size == "" {
		size = "10"
	}
	offset := (cast.ToInt(page) - 1) * cast.ToInt(size)
	limit := cast.ToInt(size)
	//绑定数据
	in.Offset = int64(offset)
	in.Limit = int64(limit)
	in.UserIdentification = userIdentification.(string)
	out, err := client.DigitRPC.NftSeriesList(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	res := map[string]interface{}{
		"total": out.Total,
		"list":  out.NftInfo,
	}
	return utils.Ok(c, res)
}

//NftResult 获取nft查询结果
func NftResult(c echo.Context) error {
	out := new(ctc.NftSearchResultResponse)
	in := new(ctc.NftSearchResultRequest)
	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	method := c.QueryParam("method")
	taskId := c.QueryParam("taskId")
	in.Method = method
	in.TaskId = taskId
	out, err := client.DigitRPC.NftSearchResult(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	return utils.Ok(c, out.Data)
}

//NftPointQuery 积分查询
func NftPointQuery(c echo.Context) error {
	out := new(ctc.NftPointQueryResponse)
	in := new(ctc.NftPointQueryRequest)
	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	userIdentification := c.Get("user_id").(string)
	in.UserIdentification = userIdentification
	out, err := client.DigitRPC.NftPointQuery(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	res := map[string]interface{}{
		"count": out.Count,
	}
	return utils.Ok(c, res)
}

//NftInfo 数字藏品详细信息
func NftInfo(c echo.Context) error {
	nftId := c.QueryParam("nft_id")
	in := new(ctc.NftInfoRequest)
	in.NftId = nftId
	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	out, err := client.DigitRPC.NftInfo(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	return utils.Ok(c, out.NftInfo)
}

// NftBuy 数字藏品购买
// @Summary 数字藏品购买
// @Tags 数字藏品
// @Accept json
// @Produce json
// @Param model body dto.NftBuyDto true " "
// @Success 200 {object} dto.RespDto
// @Failure 400 {object} dto.RespDto
// @Router /content-api/digital/nft_buy [POST]
func NftBuy(c echo.Context) error {
	buyDto := new(dto.NftBuyDto)
	if err := c.Bind(&buyDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	if err := c.Validate(buyDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	userIdentification := c.Get("user_id").(string)
	// rpc请求
	in := new(ctc.UserBuyNftRequest)
	in.SellType = int32(buyDto.SellType)
	in.PublishIdentification = buyDto.PublishIdentification
	in.SeriesId = buyDto.SeriesId
	in.UserIdentification = userIdentification
	in.Price = float32(buyDto.Price)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	out, err := client.DigitRPC.UserBuyNft(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	return utils.Ok(c, out.OrderSn)
}

//NftStatus 变更数字藏品状态
func NftStatus(c echo.Context) error {
	statusDto := new(dto.NftStatusDto)
	if err := c.Bind(&statusDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	userIdentification := c.Get("user_id").(string)
	// rpc请求
	in := new(ctc.NftStatusRequest)
	client := ctc.GetContentCenterClient()
	defer client.Close()
	in.NftId = statusDto.NftId
	in.TransStatus = int32(statusDto.TransStatus)
	in.TransPrice = int32(statusDto.TransPrice)
	in.UserIdentification = userIdentification
	out, err := client.DigitRPC.NftStatus(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	res := map[string]interface{}{
		"taskId": out.TaskId,
	}
	return utils.Ok(c, res)
}

// NftPay 数字藏品nft支付
// @Summary 数字藏品支付
// @Tags 数字藏品
// @Accept json
// @Produce json
// @Param model body dto.NftPayDto true " "
// @Success 200 {object} dto.RespDto
// @Failure 400 {object} dto.RespDto
// @Router /content-api/digital/nft_pay [POST]
func NftPay(c echo.Context) error {
	payDto := new(dto.NftPayDto)
	if err := c.Bind(&payDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	if err := c.Validate(payDto); err != nil {
		return utils.Fail(c, err.Error())
	}
	userIdentification := c.Get("user_id").(string)
	// rpc请求
	in := new(ctc.UserNftPayRequest)
	in.OrderSn = payDto.OrderSn
	in.UserIdentification = userIdentification
	client := ctc.GetContentCenterClient()
	defer client.Close()
	out, err := client.DigitRPC.UserNftPay(client.Ctx, in)
	if err != nil {
		return utils.Fail(c, err.Error())
	}
	if out.Code == 400 {
		return utils.Fail(c, out.Message)
	}
	return utils.Ok(c, out.Data)
}
