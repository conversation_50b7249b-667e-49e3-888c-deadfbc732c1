module _

go 1.15

require (
	github.com/BurntSushi/toml v1.1.0 // indirect
	github.com/alecthomas/template v0.0.0-20190718012654-fb15b899a751
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-openapi/spec v0.20.6 // indirect
	github.com/go-openapi/swag v0.21.1 // indirect
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.6.0
	github.com/golang/protobuf v1.5.2
	github.com/labstack/echo/v4 v4.7.2
	github.com/limitedlee/microservice v0.1.0
	github.com/maybgit/glog v0.1.22
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.30
	github.com/shopspring/decimal v1.2.0
	github.com/spf13/cast v1.4.1
	github.com/swaggo/echo-swagger v1.3.3
	github.com/swaggo/swag v1.8.1
	github.com/tricobbler/echo-tool v0.0.0-20210415033113-e8a087670120
	github.com/tricobbler/rp-kit v0.0.0-20210413075252-45df7834f17a
	golang.org/x/net v0.0.0-20220708220712-1185a9018129 // indirect
	golang.org/x/sys v0.0.0-20220715151400-c0bba94af5f8 // indirect
	golang.org/x/tools v0.1.11 // indirect
	google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013
	google.golang.org/grpc v1.41.0
	google.golang.org/protobuf v1.26.0
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
