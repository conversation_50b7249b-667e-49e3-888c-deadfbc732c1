package controller

import (
	"_/models"
	"_/proto/ctc"
	"_/utils"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"reflect"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

const (
	// B站广告归因时限，天
	BILI_AD_CONVERSION_DAY_LIMIT = 7
	// B站归因api2接口地址
	BILI_AD_CONVERSION_API = "https://cm.bilibili.com/conv/api/conversion/ad/cb/v1"
)

// BiliAdCallback
// @Summary B站广告推广回调地址
// @Tags 内容中心
// @Accept json
// @Produce json
// @param track_id query string true "追踪id"
// @param account_id query string true "B站账户ID"
// @param campaign_id query string true "B站计划ID"
// @param unit_id query string true "B站单元ID"
// @param creative_id query string true "B站创意ID"
// @param os query string true "客户端操作系统：0 Andriod、1 IOS、2 WindowsPhone、3 其他"
// @param imei query string false "用户终端的IMEI的md5值"
// @param callback_url query string false "回调地址，urlencode值"
// @param mac1 query string false "用户终端的eth0接口的MAC地址,大写MAC的md5值"
// @param idfa query string false "iOS idfa广告标识，适用于iOS6及以上，取md5值"
// @param caid query string false "中广协CAID"
// @param aaid query string false "安卓广告标识 Android Advertising ID"
// @param android_id query string false "用户终端安卓id，取md5值"
// @param oaid query string false "安卓设备ID，取md5值"
// @param ip query string false "投放系统服务器观察到的用户远程IP"
// @param ua query string false "数据上报终端设备的User Agent，需escape转义"
// @param model query string false "手机型号，仅支持iphone"
// @param ts query string false "客户端触发监测的时间，毫秒"
// @param shop_id query string false "店铺ID"
// @param up_mid query string false "视频UP主MID"
// @Success 200 {object} ctc.ContentResponse
// @Failure 400 {object} ctc.ContentResponse
// @Router /content-api/biliad/callback [GET]
func BiliAdCallback(c echo.Context) error {
	os := c.QueryParam("os")
	if os == "__OS__" {
		os = "-1"
	}
	clickReq := ctc.AddClickRecordRequest{
		TrackId:     checkQueryField(c.QueryParam("track_id")),
		AccountId:   checkQueryField(c.QueryParam("account_id")),            // B站账户ID
		CampaignId:  checkQueryField(c.QueryParam("campaign_id")),           // B站计划ID
		UnitId:      checkQueryField(c.QueryParam("unit_id")),               // B站单元ID
		CreativeId:  checkQueryField(c.QueryParam("creative_id")),           // B站创意ID
		Os:          cast.ToInt64(os),                                       // 客户端操作系统：0 Andriod、1 IOS、2 WindowsPhone、3 其他
		Imei:        checkQueryField(c.QueryParam("imei")),                  // 用户终端的IMEI的md5值
		CallbackUrl: checkQueryField(c.QueryParam("callback_url")),          // 回调地址，urlencode值
		Mac1:        checkQueryField(c.QueryParam("mac1")),                  // 用户终端的eth0接口的MAC地址,大写MAC的md5值
		Idfa:        checkQueryField(c.QueryParam("idfa")),                  // iOS idfa广告标识，适用于iOS6及以上，取md5值
		Caid:        checkQueryField(c.QueryParam("caid")),                  // 中广协CAID
		Aaid:        checkQueryField(c.QueryParam("aaid")),                  // 安卓广告标识 Android Advertising ID
		AndroidId:   checkQueryField(c.QueryParam("android_id")),            // 用户终端安卓id，取md5值
		Oaid:        checkQueryField(c.QueryParam("oaid")),                  // 安卓设备ID，取md5值
		Ip:          checkQueryField(c.QueryParam("ip")),                    // 投放系统服务器观察到的用户远程IP
		Ua:          checkQueryField(c.QueryParam("ua")),                    // 数据上报终端设备的User Agent，需escape转义
		Model:       checkQueryField(c.QueryParam("model")),                 // 手机型号，仅支持iphone
		Ts:          cast.ToInt64(checkQueryField(c.QueryParam("ts"))),      // 客户端触发监测的时间，毫秒
		ShopId:      cast.ToInt64(checkQueryField(c.QueryParam("shop_id"))), // 店铺ID
		UpMid:       cast.ToInt64(checkQueryField(c.QueryParam("up_mid"))),  // 视频UP主MID
	}
	if clickReq.TrackId == "" {
		return c.JSON(400, ctc.ContentResponse{Code: 400, Message: "参数错误"})
	}
	client := ctc.GetContentCenterClient()
	defer client.Close()
	re, err := client.BiliAdRPC.AddClickRecord(client.Ctx, &clickReq)
	if err != nil {
		return c.JSON(500, ctc.ContentResponse{Code: 500, Message: "保存记录失败"})
	}
	return c.JSON(int(re.Code), re)
}

// 检测回调参数是否有值，
func checkQueryField(str string) string {
	if strings.HasPrefix(str, "__") {
		return ""
	}
	return str
}

// CheckBiliAdConversion
// @Summary 校验B站广告归因
// @Tags 内容中心
// @Accept json
// @Produce json
// @Param model body models.BiliAdConversion true " "
// @Success 200 {object} ctc.ContentResponse
// @Failure 400 {object} ctc.ContentResponse
// @Router /content-api/biliad/check-conv [POST]
func CheckBiliAdConversion(c echo.Context) error {
	p := models.BiliAdConversion{}
	if err := c.Bind(&p); err != nil {
		return c.JSON(400, ctc.ContentResponse{Code: 400, Message: "参数错误1"})
	}
	if p.ConvType == "" {
		return c.JSON(400, ctc.ContentResponse{Code: 400, Message: "参数错误2"})
	}
	if p.Imei == "" && p.Idfa == "" && p.Oaid == "" &&
		(p.ClientIp == "" || p.Ua == "") {
		return c.JSON(400, ctc.ContentResponse{Code: 400, Message: "参数错误3"})
	}
	glog.Info("CheckBiliAdConversion校验B站广告归因接口参数，params:", p)

	// 校验timestamp、nonce、sign
	if time.Now().Unix()-p.Timestamp > 60 {
		return c.JSON(200, ctc.ContentResponse{Code: 200, Message: "无效请求1"})
	}
	key := fmt.Sprintf("content-api.CheckBiliAdConversion.%d.%s", p.Os, p.Nonce)
	if v := utils.RedisClient.Get(key).Val(); v == "1" {
		return c.JSON(200, ctc.ContentResponse{Code: 200, Message: "无效请求2"})
	}
	if err := checkSign(&p); err != nil {
		glog.Error("校验B站广告归因sign错误，params:", p)
		return c.JSON(200, ctc.ContentResponse{Code: 200, Message: err.Error()})
	}
	utils.RedisClient.Set(key, "1", 60*time.Second)

	// 参数转md5
	formatConversionArgs(&p)

	rpcClient := ctc.GetContentCenterClient()
	defer rpcClient.Close()

	// 查询是否为推广用户
	re, err := rpcClient.BiliAdRPC.FindClickRecords(rpcClient.Ctx, getFindClickRecordsRequest(&p))
	if err != nil {
		return c.JSON(500, ctc.ContentResponse{Code: 500, Message: err.Error()})
	}
	if len(re.List) == 0 {
		glog.Info("CheckBiliAdConversion未找到B站广告点击记录，params:", p)
		return c.JSON(200, ctc.ContentResponse{Code: 200, Message: ""}) // 非广告归因
	}
	clickRecord := re.List[0]

	// 回调B站接口
	convTime := time.Now().UnixNano() / 1e6
	bRe, err := biliAdConversion(&p, clickRecord.TrackId, convTime)
	if err != nil {
		glog.Error("调用B站广告转化回传接口失败：" + err.Error())
		return c.JSON(500, ctc.ContentResponse{Code: 500, Message: err.Error()})
	}

	// 保存回传Bili结果
	covRe, err := rpcClient.BiliAdRPC.SaveConversion(rpcClient.Ctx, &ctc.SaveConversionRequest{
		BiliAdClickId: clickRecord.Id,
		ConvType:      p.ConvType,
		ConvTime:      convTime,
		Imei:          p.Imei,
		Idfa:          p.Idfa,
		Oaid:          p.Oaid,
		Mac:           p.Mac,
		ClientIp:      p.ClientIp,
		Model:         p.Model,
		TrackId:       clickRecord.TrackId,
		Ua:            p.Ua,
		ResultCode:    bRe.Code,
		ResultMessage: bRe.Message,
	})
	if err != nil {
		return c.JSON(500, ctc.ContentResponse{Code: 500, Message: err.Error()})
	}
	return c.JSON(200, covRe)
}

func checkSign(p *models.BiliAdConversion) error {
	keys := make([]string, 0)
	data := make(map[string]string, 0)

	v := reflect.ValueOf(*p)
	typ := reflect.TypeOf(*p)
	count := v.NumField()
	for i := 0; i < count; i++ {
		k := typ.Field(i).Tag.Get("json")
		if k == "sign" {
			continue
		}
		keys = append(keys, k)
		f := v.Field(i)
		switch f.Kind() {
		case reflect.String:
			data[k] = fmt.Sprintf("%s%s", k, f.String())
		case reflect.Int64:
			data[k] = fmt.Sprintf("%s%d", k, f.Int())
		}
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	values := make([]string, 0)
	for _, k := range keys {
		values = append(values, data[k])
	}
	if p.Sign != kit.GetMd5(strings.Join(values, "")) {
		return errors.New("sing错误")
	}
	return nil
}

func formatConversionArgs(p *models.BiliAdConversion) {
	// 转md5
	if p.Imei != "" {
		p.Imei = kit.GetMd5(p.Imei)
	}
	if p.Idfa != "" {
		p.Idfa = kit.GetMd5(p.Idfa)
	}
	if p.Oaid != "" {
		p.Oaid = kit.GetMd5(p.Oaid)
	}
	if p.Mac != "" {
		p.Mac = kit.GetMd5(strings.ToUpper(p.Mac))
	}
	// ua escape转义
	if p.Ua != "" {
		p.Ua, _ = url.QueryUnescape(p.Ua)
	}
}

func getFindClickRecordsRequest(p *models.BiliAdConversion) *ctc.FindClickRecordsRequest {
	req := ctc.FindClickRecordsRequest{
		Imei:      p.Imei,
		Os:        p.Os,
		CreatedAt: time.Now().AddDate(0, 0, -BILI_AD_CONVERSION_DAY_LIMIT).Format("2006-01-02 15:04:05"),
	}
	if p.Os == 1 && p.Idfa != "" { // iOS
		req.Idfa = p.Idfa
	} else if p.Os != 1 && p.Oaid != "" { // 安卓 或 其他
		req.Oaid = p.Oaid
	} else {
		req.Ip = p.ClientIp
		if p.Os == 1 { // ios
			req.Ua = getIosUa(p.Ua)
		} else {
			req.Ua = getAndroidUa(p.Ua, p.Model)
		}
	}
	return &req
}

// 获取ios手机UA
// 阿闻：Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
// B站：Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X) AppleWebKit/613.******** (KHTML, like Gecko) Mobile/19E258
func getIosUa(ua string) string {
	reg, _ := regexp.Compile(`[(]([^()]*?)[)]`)
	arr := reg.FindStringSubmatch(ua)
	if len(arr) > 0 {
		return arr[0]
	}
	return ua
}

// 获取安卓手机UA
// 阿闻：Mozilla/5.0 (Linux; Android 10; OXF-AN00 Build/HUAWEIOXF-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36
// B站：Dalvik/2.1.0 (Linux; U; Android 10; OXF-AN00 Build/HUAWEIOXF-AN00)
func getAndroidUa(ua, model string) string {
	fIdx := strings.Index(ua, "Android")
	if fIdx >= 0 && model != "" {
		lIdx := strings.LastIndex(ua, model)
		return ua[fIdx : lIdx+len(model)]
	}

	// 匹配第一个()里的内容
	reg, _ := regexp.Compile(`[(]([^()]*?)[)]`)
	arr := reg.FindStringSubmatch(ua)
	if len(arr) > 0 {
		temp := strings.Split(arr[0], ";")
		if len(temp) > 3 {
			// 取中间一段值
			return strings.Join(temp[1:len(temp)-1], ";")
		} else {
			return arr[0]
		}
	}
	return ua
}

type BiliApiResult struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
}

// 转化回传B站接口
func biliAdConversion(p *models.BiliAdConversion, trackId string, convTime int64) (*BiliApiResult, error) {
	params := map[string]interface{}{
		"conv_type": p.ConvType,
		"conv_time": convTime,
		"imei":      p.Imei,
		"idfa":      p.Idfa,
		"oaid":      p.Oaid,
		"mac":       p.Mac,
		"client_ip": p.ClientIp,
		"model":     url.QueryEscape(p.Model),
		"track_id":  trackId,
		"ua":        url.QueryEscape(p.Ua),
	}
	queryStrs := make([]string, 0)
	for k, v := range params {
		queryStrs = append(queryStrs, fmt.Sprintf("%s=%v", k, v))
	}
	re := &BiliApiResult{}
	httpResp, err := http.Get(BILI_AD_CONVERSION_API + "?" + strings.Join(queryStrs, "&"))
	if err != nil {
		return re, err
	}
	defer httpResp.Body.Close()
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("请求B站回传接口失败：%s", httpResp.Status)
	}
	json.NewDecoder(httpResp.Body).Decode(re)
	return re, nil
}
