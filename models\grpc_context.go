package models

type LoginUserInfo struct {
	//
	Iss string `json:"iss"`
	//
	Aud string `json:"aud"`
	//
	Jti string `json:"jti"`
	//
	Iat string `json:"iat"`
	//
	Nbf string `json:"nbf"`
	//
	Exp string `json:"exp"`

	ObjectId     string `json:"object_id"`     //对象编号 - 用户或医生
	ObjectName   string `json:"object_name"`   //对象名称 - 用户或医生
	ObjectMobile string `json:"object_mobile"` //对象手机号 - 用户或医生
	OpenId       string `json:"open_id"`       //微信小程序用户唯一标识
}

type PlatformChannel struct {
	ChannelId int
	UserAgent int
}

type GrpcContext struct {
	UserInfo LoginUserInfo
	Channel  PlatformChannel
}
