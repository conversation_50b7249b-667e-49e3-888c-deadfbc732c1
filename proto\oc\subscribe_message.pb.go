// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/subscribe_message.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PushTemplateRequest struct {
	//用户ID
	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId"`
	//订单编号
	OrderSn string `protobuf:"bytes,2,opt,name=orderSn,proto3" json:"orderSn"`
	//模板ID
	TemplateId string `protobuf:"bytes,3,opt,name=templateId,proto3" json:"templateId"`
	//类型(1=>发送退款成功通知, 2=>发送退款失败通知, 3=>发送退款状态通知, 4=>推送尾款支付提醒通知)
	PushType int32 `protobuf:"varint,4,opt,name=pushType,proto3" json:"pushType"`
	//备注
	Remarks string `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks"`
	//发送退款成功通知
	RefundSuccess *RefundSuccess `protobuf:"bytes,6,opt,name=refundSuccess,proto3" json:"refundSuccess"`
	//发送退款失败通知
	RefundFail *RefundFail `protobuf:"bytes,7,opt,name=refundFail,proto3" json:"refundFail"`
	//发送退款状态通知
	RefundStatus *RefundStatus `protobuf:"bytes,8,opt,name=refundStatus,proto3" json:"refundStatus"`
	//预售尾款支付通知
	PreSalePay *PreSalePay `protobuf:"bytes,9,opt,name=preSalePay,proto3" json:"preSalePay"`
	//主体:1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,10,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTemplateRequest) Reset()         { *m = PushTemplateRequest{} }
func (m *PushTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*PushTemplateRequest) ProtoMessage()    {}
func (*PushTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{0}
}

func (m *PushTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTemplateRequest.Unmarshal(m, b)
}
func (m *PushTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTemplateRequest.Marshal(b, m, deterministic)
}
func (m *PushTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTemplateRequest.Merge(m, src)
}
func (m *PushTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_PushTemplateRequest.Size(m)
}
func (m *PushTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PushTemplateRequest proto.InternalMessageInfo

func (m *PushTemplateRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *PushTemplateRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PushTemplateRequest) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *PushTemplateRequest) GetPushType() int32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *PushTemplateRequest) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *PushTemplateRequest) GetRefundSuccess() *RefundSuccess {
	if m != nil {
		return m.RefundSuccess
	}
	return nil
}

func (m *PushTemplateRequest) GetRefundFail() *RefundFail {
	if m != nil {
		return m.RefundFail
	}
	return nil
}

func (m *PushTemplateRequest) GetRefundStatus() *RefundStatus {
	if m != nil {
		return m.RefundStatus
	}
	return nil
}

func (m *PushTemplateRequest) GetPreSalePay() *PreSalePay {
	if m != nil {
		return m.PreSalePay
	}
	return nil
}

func (m *PushTemplateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//通用返回
type PushTemplateResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTemplateResponse) Reset()         { *m = PushTemplateResponse{} }
func (m *PushTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*PushTemplateResponse) ProtoMessage()    {}
func (*PushTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{1}
}

func (m *PushTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTemplateResponse.Unmarshal(m, b)
}
func (m *PushTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTemplateResponse.Marshal(b, m, deterministic)
}
func (m *PushTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTemplateResponse.Merge(m, src)
}
func (m *PushTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_PushTemplateResponse.Size(m)
}
func (m *PushTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PushTemplateResponse proto.InternalMessageInfo

func (m *PushTemplateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PushTemplateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PushTemplateResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//发送退款成功通知
type RefundSuccess struct {
	//退款ID
	RefundId string `protobuf:"bytes,1,opt,name=refundId,proto3" json:"refundId"`
	//退款类型
	RefundType string `protobuf:"bytes,2,opt,name=refundType,proto3" json:"refundType"`
	//退款金额
	RefundAmount string `protobuf:"bytes,3,opt,name=refundAmount,proto3" json:"refundAmount"`
	//退款时间
	RefundTime           string   `protobuf:"bytes,4,opt,name=refundTime,proto3" json:"refundTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundSuccess) Reset()         { *m = RefundSuccess{} }
func (m *RefundSuccess) String() string { return proto.CompactTextString(m) }
func (*RefundSuccess) ProtoMessage()    {}
func (*RefundSuccess) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{2}
}

func (m *RefundSuccess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundSuccess.Unmarshal(m, b)
}
func (m *RefundSuccess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundSuccess.Marshal(b, m, deterministic)
}
func (m *RefundSuccess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundSuccess.Merge(m, src)
}
func (m *RefundSuccess) XXX_Size() int {
	return xxx_messageInfo_RefundSuccess.Size(m)
}
func (m *RefundSuccess) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundSuccess.DiscardUnknown(m)
}

var xxx_messageInfo_RefundSuccess proto.InternalMessageInfo

func (m *RefundSuccess) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *RefundSuccess) GetRefundType() string {
	if m != nil {
		return m.RefundType
	}
	return ""
}

func (m *RefundSuccess) GetRefundAmount() string {
	if m != nil {
		return m.RefundAmount
	}
	return ""
}

func (m *RefundSuccess) GetRefundTime() string {
	if m != nil {
		return m.RefundTime
	}
	return ""
}

//发送退款失败通知
type RefundFail struct {
	//退款ID
	RefundId string `protobuf:"bytes,1,opt,name=refundId,proto3" json:"refundId"`
	//退款类型
	RefundType string `protobuf:"bytes,2,opt,name=refundType,proto3" json:"refundType"`
	//退款订单
	RefundSn string `protobuf:"bytes,3,opt,name=refundSn,proto3" json:"refundSn"`
	//状态
	Status               string   `protobuf:"bytes,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundFail) Reset()         { *m = RefundFail{} }
func (m *RefundFail) String() string { return proto.CompactTextString(m) }
func (*RefundFail) ProtoMessage()    {}
func (*RefundFail) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{3}
}

func (m *RefundFail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundFail.Unmarshal(m, b)
}
func (m *RefundFail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundFail.Marshal(b, m, deterministic)
}
func (m *RefundFail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundFail.Merge(m, src)
}
func (m *RefundFail) XXX_Size() int {
	return xxx_messageInfo_RefundFail.Size(m)
}
func (m *RefundFail) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundFail.DiscardUnknown(m)
}

var xxx_messageInfo_RefundFail proto.InternalMessageInfo

func (m *RefundFail) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *RefundFail) GetRefundType() string {
	if m != nil {
		return m.RefundType
	}
	return ""
}

func (m *RefundFail) GetRefundSn() string {
	if m != nil {
		return m.RefundSn
	}
	return ""
}

func (m *RefundFail) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

//发送退款状态通知
type RefundStatus struct {
	//退款ID
	RefundId string `protobuf:"bytes,1,opt,name=refundId,proto3" json:"refundId"`
	//状态
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status"`
	//退款类型
	RefundType           string   `protobuf:"bytes,3,opt,name=refundType,proto3" json:"refundType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundStatus) Reset()         { *m = RefundStatus{} }
func (m *RefundStatus) String() string { return proto.CompactTextString(m) }
func (*RefundStatus) ProtoMessage()    {}
func (*RefundStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{4}
}

func (m *RefundStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundStatus.Unmarshal(m, b)
}
func (m *RefundStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundStatus.Marshal(b, m, deterministic)
}
func (m *RefundStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundStatus.Merge(m, src)
}
func (m *RefundStatus) XXX_Size() int {
	return xxx_messageInfo_RefundStatus.Size(m)
}
func (m *RefundStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundStatus.DiscardUnknown(m)
}

var xxx_messageInfo_RefundStatus proto.InternalMessageInfo

func (m *RefundStatus) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *RefundStatus) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *RefundStatus) GetRefundType() string {
	if m != nil {
		return m.RefundType
	}
	return ""
}

type PreSalePay struct {
	//尾款支付开始时间
	StartTime string `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime"`
	//尾款支付结束时间
	EndTime string `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime"`
	//温馨提醒
	Remarks string `protobuf:"bytes,3,opt,name=remarks,proto3" json:"remarks"`
	//是否虚拟订单 0 否 1是
	IsVirtual            int32    `protobuf:"varint,4,opt,name=isVirtual,proto3" json:"isVirtual"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreSalePay) Reset()         { *m = PreSalePay{} }
func (m *PreSalePay) String() string { return proto.CompactTextString(m) }
func (*PreSalePay) ProtoMessage()    {}
func (*PreSalePay) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{5}
}

func (m *PreSalePay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreSalePay.Unmarshal(m, b)
}
func (m *PreSalePay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreSalePay.Marshal(b, m, deterministic)
}
func (m *PreSalePay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreSalePay.Merge(m, src)
}
func (m *PreSalePay) XXX_Size() int {
	return xxx_messageInfo_PreSalePay.Size(m)
}
func (m *PreSalePay) XXX_DiscardUnknown() {
	xxx_messageInfo_PreSalePay.DiscardUnknown(m)
}

var xxx_messageInfo_PreSalePay proto.InternalMessageInfo

func (m *PreSalePay) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *PreSalePay) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PreSalePay) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *PreSalePay) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func init() {
	proto.RegisterType((*PushTemplateRequest)(nil), "oc.PushTemplateRequest")
	proto.RegisterType((*PushTemplateResponse)(nil), "oc.PushTemplateResponse")
	proto.RegisterType((*RefundSuccess)(nil), "oc.RefundSuccess")
	proto.RegisterType((*RefundFail)(nil), "oc.RefundFail")
	proto.RegisterType((*RefundStatus)(nil), "oc.RefundStatus")
	proto.RegisterType((*PreSalePay)(nil), "oc.PreSalePay")
}

func init() { proto.RegisterFile("oc/subscribe_message.proto", fileDescriptor_8dd35fdf2f57ee62) }

var fileDescriptor_8dd35fdf2f57ee62 = []byte{
	// 488 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x54, 0x4d, 0x8b, 0xd4, 0x40,
	0x10, 0x35, 0x33, 0x9b, 0xd9, 0x9d, 0x72, 0x56, 0xb4, 0x5d, 0xdd, 0x66, 0x58, 0x64, 0xc8, 0x69,
	0x4e, 0x23, 0xac, 0x82, 0x67, 0x11, 0x84, 0x39, 0x08, 0x43, 0x47, 0x3c, 0x78, 0x70, 0xe9, 0x49,
	0xca, 0x31, 0x38, 0x49, 0xc7, 0xee, 0x8e, 0xb0, 0xe0, 0xcd, 0x1f, 0xe0, 0x5f, 0x96, 0xfe, 0x48,
	0xba, 0xb3, 0x0b, 0x5e, 0xbc, 0xe5, 0xbd, 0xee, 0xaa, 0x57, 0x5d, 0xaf, 0x2a, 0xb0, 0x14, 0xc5,
	0x4b, 0xd5, 0xed, 0x55, 0x21, 0xab, 0x3d, 0xde, 0xd4, 0xa8, 0x14, 0x3f, 0xe0, 0xa6, 0x95, 0x42,
	0x0b, 0x32, 0x11, 0x45, 0xf6, 0x7b, 0x0a, 0x4f, 0x77, 0x9d, 0xfa, 0xf6, 0x11, 0xeb, 0xf6, 0xc8,
	0x35, 0x32, 0xfc, 0xd1, 0xa1, 0xd2, 0xe4, 0x39, 0xcc, 0x44, 0x8b, 0xcd, 0xb6, 0xa4, 0xc9, 0x2a,
	0x59, 0xcf, 0x99, 0x47, 0x84, 0xc2, 0xa9, 0x90, 0x25, 0xca, 0xbc, 0xa1, 0x13, 0x7b, 0xd0, 0x43,
	0xf2, 0x02, 0x40, 0xfb, 0x24, 0xdb, 0x92, 0x4e, 0xed, 0x61, 0xc4, 0x90, 0x25, 0x9c, 0xb5, 0x46,
	0xe8, 0xb6, 0x45, 0x7a, 0xb2, 0x4a, 0xd6, 0x29, 0x1b, 0xb0, 0xc9, 0x2a, 0xb1, 0xe6, 0xf2, 0xbb,
	0xa2, 0xa9, 0xcb, 0xea, 0x21, 0x79, 0x03, 0xe7, 0x12, 0xbf, 0x76, 0x4d, 0x99, 0x77, 0x45, 0x81,
	0x4a, 0xd1, 0xd9, 0x2a, 0x59, 0x3f, 0xbc, 0x7e, 0xb2, 0x11, 0xc5, 0x86, 0xc5, 0x07, 0x6c, 0x7c,
	0x8f, 0x6c, 0x00, 0x1c, 0xf1, 0x9e, 0x57, 0x47, 0x7a, 0x6a, 0xa3, 0x1e, 0x85, 0x28, 0xc3, 0xb2,
	0xe8, 0x06, 0x79, 0x0d, 0x0b, 0x9f, 0x40, 0x73, 0xdd, 0x29, 0x7a, 0x66, 0x23, 0x1e, 0x47, 0x3a,
	0x96, 0x67, 0xa3, 0x5b, 0x46, 0xa5, 0x95, 0x98, 0xf3, 0x23, 0xee, 0xf8, 0x2d, 0x9d, 0x07, 0x95,
	0xdd, 0xc0, 0xb2, 0xe8, 0x06, 0x79, 0x06, 0x33, 0x21, 0x0f, 0x37, 0x55, 0x49, 0xc1, 0xb6, 0x20,
	0x15, 0xf2, 0xb0, 0x2d, 0xb3, 0xcf, 0x70, 0x31, 0x36, 0x41, 0xb5, 0xa2, 0x51, 0x48, 0x08, 0x9c,
	0x14, 0xa2, 0x44, 0xeb, 0x41, 0xca, 0xec, 0xb7, 0xe9, 0x95, 0xb7, 0xb1, 0x77, 0xc0, 0x43, 0x72,
	0x01, 0x29, 0x4a, 0x29, 0xa4, 0x6f, 0xbe, 0x03, 0xd9, 0x9f, 0x04, 0xce, 0x47, 0x9d, 0x32, 0x4e,
	0xb8, 0x47, 0x0c, 0xee, 0x0e, 0xd8, 0xb8, 0xe8, 0xbe, 0xad, 0x4f, 0x4e, 0x20, 0x62, 0x48, 0xd6,
	0xb7, 0xe9, 0x6d, 0x2d, 0xba, 0x46, 0x7b, 0xa9, 0x11, 0x17, 0xe5, 0xa8, 0x6a, 0xe7, 0x75, 0xc8,
	0x51, 0xd5, 0x98, 0xfd, 0x02, 0x08, 0x26, 0xfc, 0x57, 0x35, 0x43, 0x6c, 0xde, 0xf8, 0x4a, 0x06,
	0x6c, 0x26, 0x58, 0x39, 0x2b, 0x5d, 0x05, 0x1e, 0x65, 0x7b, 0x58, 0xc4, 0x86, 0xfe, 0x53, 0x3f,
	0xe4, 0x98, 0xc4, 0x39, 0xee, 0xd4, 0x35, 0xbd, 0x5b, 0x97, 0x79, 0x61, 0x18, 0x00, 0x72, 0x05,
	0x73, 0xa5, 0xb9, 0xd4, 0xb6, 0x1d, 0x4e, 0x22, 0x10, 0xc6, 0x4f, 0xf4, 0xad, 0xf2, 0x7e, 0x7a,
	0x18, 0x6f, 0xc5, 0x74, 0xbc, 0x15, 0x57, 0x30, 0xaf, 0xd4, 0xa7, 0x4a, 0xea, 0x8e, 0x1f, 0xfd,
	0x32, 0x05, 0xe2, 0xfa, 0x0b, 0x5c, 0xe6, 0xfd, 0xca, 0x7f, 0x70, 0xb3, 0x91, 0xa3, 0xfc, 0x59,
	0x15, 0x48, 0xde, 0xc1, 0x22, 0x1e, 0x34, 0x72, 0x69, 0x67, 0xf5, 0xfe, 0xfe, 0x2f, 0xe9, 0xfd,
	0x03, 0x37, 0x93, 0xd9, 0x83, 0xfd, 0xcc, 0xfe, 0x3e, 0x5e, 0xfd, 0x0d, 0x00, 0x00, 0xff, 0xff,
	0xd0, 0xd0, 0x2a, 0xd0, 0x5c, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SubscribeMessageServiceClient is the client API for SubscribeMessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SubscribeMessageServiceClient interface {
	// @Desc    	发送消息
	PushTemplate(ctx context.Context, in *PushTemplateRequest, opts ...grpc.CallOption) (*PushTemplateResponse, error)
}

type subscribeMessageServiceClient struct {
	cc *grpc.ClientConn
}

func NewSubscribeMessageServiceClient(cc *grpc.ClientConn) SubscribeMessageServiceClient {
	return &subscribeMessageServiceClient{cc}
}

func (c *subscribeMessageServiceClient) PushTemplate(ctx context.Context, in *PushTemplateRequest, opts ...grpc.CallOption) (*PushTemplateResponse, error) {
	out := new(PushTemplateResponse)
	err := c.cc.Invoke(ctx, "/oc.SubscribeMessageService/PushTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubscribeMessageServiceServer is the server API for SubscribeMessageService service.
type SubscribeMessageServiceServer interface {
	// @Desc    	发送消息
	PushTemplate(context.Context, *PushTemplateRequest) (*PushTemplateResponse, error)
}

// UnimplementedSubscribeMessageServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSubscribeMessageServiceServer struct {
}

func (*UnimplementedSubscribeMessageServiceServer) PushTemplate(ctx context.Context, req *PushTemplateRequest) (*PushTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushTemplate not implemented")
}

func RegisterSubscribeMessageServiceServer(s *grpc.Server, srv SubscribeMessageServiceServer) {
	s.RegisterService(&_SubscribeMessageService_serviceDesc, srv)
}

func _SubscribeMessageService_PushTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeMessageServiceServer).PushTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.SubscribeMessageService/PushTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeMessageServiceServer).PushTemplate(ctx, req.(*PushTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SubscribeMessageService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.SubscribeMessageService",
	HandlerType: (*SubscribeMessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushTemplate",
			Handler:    _SubscribeMessageService_PushTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/subscribe_message.proto",
}
