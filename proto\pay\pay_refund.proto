syntax = "proto3";
package pay;
import "google/api/annotations.proto";
import "pay/common.proto";

// @Desc    			支付订单退款请求
// <AUTHOR>
// @Date		 			2020-06-11
message PayRefundRequest{
	string TradeNo       = 1;       // 支付中心流水号
	int32  RefundAmt     = 2;       // 退款金额
	string CallbackUrl   = 3;       // 后台回调地址
	string BackParam     = 4;       // 商户私有域
	string ExtendInfo    = 5;       // 扩展信息
	string MerchantNo    = 6;       // 商户号
	string ClientIP      = 7;       // 客户端 IP
	string Sign          = 8;       // 签名
	string domain        = 9;       // 请求域名
	int32  AppId         = 10;      // 应用id
  string  refund_id    = 11;      // 商户退款订单号
}

// @Desc    			支付订单退款查询请求
// <AUTHOR>
// @Date		 			2020-06-10
message PayRefundQueryRequest{
	string MerchantNo    = 1;       // 商户号
	string TransactionNo = 2;       // 支付中心流水号
	string ClientIP      = 3;       // 客户端 IP
	string RefundId      = 4;       // 退款订单号
	int32  RefundAmt     = 5;       // 退款金额
	string Sign          = 6;       // 签名
}

// @Desc    			电银退款结果异步回调通知请求
// <AUTHOR>
// @Date		 			2020-06-10
message RefundNoticeRequest{
	string charset      = 1;       // 字符集 固定值：00，代表 GBK
	string version      = 2;       // 版本号 固定值：1.1
	string signType     = 3;       // 签名类型 固定值：RSA
	string serverCert   = 4;       // 服务证书 服务器端的公钥证书
	string serverSign   = 5;       // 签名 详见：安全规范
	string orderId      = 6;       // 商户订单号 仅能用大小写字母与数字，且在商户系统具有唯一性
	string refundId     = 7;       // 退款订单号
	string tradeNo      = 8;       // 退款流水号 电银生成的退款流水号
	string merchantId   = 9;       // 商户号 支付平台给合作平台分配的唯一标识
	int32  transAmt     = 10;      // 退款金额 单位为分
	string refundDate   = 11;      // 退款时间 YYYYMMDD
	string transState   = 12;      // 退款状态 S: 成功(失败不通知)
	string backParam    = 13;      // 商户私有域 原样返回给商户
	string extendInfo   = 14;      // 扩展信息 预留字段，JSON 格式
}

// @Desc    			电银回调通知 HTTP 响应结果 Data
// <AUTHOR>
// @Date		 		  2020-06-11
message RefundNoticeResponse{
    string  result	    = 1;	// 结果响应
}

// @Desc    			HTTP 响应结果 Data
// <AUTHOR>
// @Date		 			2020-06-11
message PayRefundReturnData{
    string  backParam	      = 1;	// 商户私有域：交易返回时原样返回给商户网站，给商户备用
    string  callbackUrl	    = 2;	// 后台回调地址
    string  clientIP	      = 3;	// 客户端 IP
    string  extendInfo	    = 5;	// 扩展信息：预留字段，JSON 格式
    string  refundAmt	      = 6;	// 退款金额，以分为单位
    string  refundId	      = 7;	// 退款订单号
    string  rspCode	        = 8;	// 退款状态 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
    string  rspMessage	    = 9;	// 返回信息
    string  transactionNo   = 10;	// 交易流水号
    string  merchantId      = 11;	// 商户号
    string  sign	          = 12;	// 签名
}

// @Desc    			HTTP请求响应
// <AUTHOR>
// @Date		 			2020-06-11
message PayRefundResponse {
    int32                            code = 1;
    string                            message = 2;
    PayRefundReturnData data = 3;
    PayRefundReturnBaiduData baiduData = 4;
}

message PayRefundReturnBaiduData {
    // 平台退款批次号
    string refundBatchId = 1;
    // 平台可退退款金额【分为单位】
    int64 refundPayMoney = 2;
}

message QueryRefundStatusRequest{
    //商户订单号(原交易)
    string mer_order_no = 1;
    //商户退款单号
    string mer_refund_order_no = 2;
    //交易码,非银联：PF2 银联：QRY3 说明：PF2 为被扫交易类型，支持微信、支付宝、云闪付、电银支付等类型； QRY3 仅支持云闪付（老接口兼容性保留值）
    string trancde = 3;
    //请求头信息
    ScanHeadBase head_base = 4;
}
message QueryRefundStatusResponse{
    //返回码
    int32 code = 1;
    string message =2;
    //商户订单号
    string mer_order_no = 3;
    //商户退款单号
    string mer_refund_order_no = 4;
    //退款金额 单位：分
    string refund_amount = 5;
    //支付结果 S：成功 R：正在执行 F：失败
    string refund_result = 6;
    //实际退款金额 单位：分
    string actual_refund_amount = 7;
    //退款时间
    string refund_time = 8;
    //退款流水号
    string refund_no = 9;
}
message NewRefundRequest {
    //商户订单号(原交易)
    string mer_order_no = 1;
    //交易码：非银联时（微信，支付宝）：P02 银联：CSU03
    string trancde = 2;
    //退款金额  单位：分
    string refund_amount = 3;
    //退款备注
    string refund_remark = 4;
    //请求头信息
    ScanHeadBase head_base = 5;
    //后台回调地址（支付中心回调电商）
    string notify_url = 6;
    // 客户端 IP
    string  clientIP = 7;
    string backParam = 8;      // 商户私有域 原样返回给商户
    string extendInfo = 9;      // 扩展信息 预留字段，JSON 格式
    int32  AppId = 10;      // 应用id
}
message MercDetail {
    //分账类型 固定：MERCHANT_ID
    string type =1 ;
    //商户号
    string recMerchantId = 2;
    //分账金额
    string payAmt = 3;
    //分账描述
    string description =4;
}
message NewRefundResponse{
    //返回码
    int32 code = 1;
    string message =2;
    //商户订单号
    string mer_order_no = 3;
    //商户退款单号
    string mer_refund_order_no = 4;
    //退款金额 单位：分
    string refund_amount = 5;
    //支付结果 S：成功 R：正在执行 F：失败
    string refund_result = 6;
    //实际退款金额 单位：分
    string actual_refund_amount = 7;
    //退款时间
    string refund_time = 8;
    //退款流水号
    string refund_no = 9;
}

// @Desc    			HTTP请求响应
// <AUTHOR>
// @Date		 			2023-04-15
message DYRefundInfoResponse {
  int32   code = 1;
  string  message = 2;
  DYRefundInfoData data = 3;
  message DYRefundInfoData{
    //退款状态  S:退款成功  F:退款失败  P:退款处理中
      string refund_state = 1;
      //支付订单号
      string order_Id = 2;
      //退款手续费(返回空就是手续费没结算)
      string fee_amt = 3;
      //退款订单号
      string trade_no = 4;
  }
}

message DYRefundInfoRequest{
  string merchant_id = 1;
  string trade_no = 2;
}

// @Desc    			订单退款
// <AUTHOR>
// @Date		 			2020-06-11
service PayRefund {
    // @Desc    			支付退款请求
    // <AUTHOR>
    // @Date		 			2020-06-11
    rpc PayRefund (PayRefundRequest) returns (PayRefundResponse) {}

    // @Desc    			支付退款查询
    // <AUTHOR>
    // @Date		 			2020-06-11
    rpc PayRefundQuery (PayRefundQueryRequest) returns (PayRefundResponse) {}

    // @Desc    			电银退款结果异步回调通知
    // <AUTHOR>
    // @Date		 			2020-06-11
    rpc PayRefundNotice (RefundNoticeRequest) returns (RefundNoticeResponse) {}

    // 发起退款
    rpc NewRefund (NewRefundRequest) returns (PayRefundResponse) {}
    // 退款查询
    rpc QueryRefundStatus (QueryRefundStatusRequest) returns (QueryRefundStatusResponse) {}

    rpc Refund (PayRefundRequest) returns (PayRefundResponse) {}


    // @Desc    			退款查询（手续费）暂时只能查询电银线上帐号的退款
    // <AUTHOR>
    // @Date		 			2023-04-15
    rpc DYRefundInfo (DYRefundInfoRequest) returns (DYRefundInfoResponse) {}
}

