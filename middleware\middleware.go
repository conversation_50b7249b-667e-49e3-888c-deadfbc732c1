package middleware

import (
	"_/models"
	"_/utils"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

var NotAuthPath = map[string]int{}

var NotMushAuthPath = map[string]int{} //有登录头,就解析登录头,没有就不解析

func init() {
	NotAuthPath["/content-api/advertisement/get"] = 1
	NotAuthPath["/content-api/article/category_bar"] = 1
	NotAuthPath["/content-api/article/recommend"] = 1
	NotAuthPath["/content-api/article/statistics"] = 1
	NotAuthPath["/content-api/article/detail"] = 1
	NotAuthPath["/content-api/article/tips"] = 1
	NotAuthPath["/content-api/article/list"] = 1
	NotAuthPath["/content-api/article/mini-filter"] = 1
	NotAuthPath["/content-api/census/visit"] = 1
	NotAuthPath["/content-api/census/boehringere"] = 1
	NotAuthPath["/swagger/*"] = 1
	NotAuthPath["/favicon.ico"] = 1
	NotAuthPath["/content-api/biliad/callback"] = 1
	NotAuthPath["/content-api/biliad/check-conv"] = 1
	NotAuthPath["/content-api/biliad/check-conv/token"] = 1
	NotAuthPath["/content-api/digital/register"] = 1
	NotAuthPath["/content-api/digital/nft_list"] = 1
	NotAuthPath["/content-api/digital/nft_result"] = 1
	NotAuthPath["/content-api/digital/nft_info"] = 1
	NotAuthPath["/content-api/digital/nft_create"] = 1
	NotAuthPath["/content-api/digital/nft_update"] = 1
	NotAuthPath["/content-api/digital/nft_del/:id"] = 1
	NotAuthPath["/content-api/digital/nft_flat_list"] = 1
	NotAuthPath["/content-api/digital/auth_openid"] = 1
	NotMushAuthPath["/content-api/digital/nft_flat_detail"] = 1
}

//校验渠道id和来源，并写入context
func CheckChannel() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			//是否过滤授权判断
			if _, ok := NotAuthPath[c.Path()]; ok {
				return next(c)
			}
			if _, ok := NotMushAuthPath[c.Path()]; ok {
				if c.Request().Header.Get("Authorization") == "" {
					c.Set("user_id", "")
					return next(c)
				}
			}
			claims, err := utils.GetPayloadDirectly(c)
			if err != nil {
				glog.Error("登录信息验证失败， 查看用户的数据的登录信息：", err, c.Path())
				out := models.BaseResponse{Msg: fmt.Sprintf("valid token required.%v", err)}
				return c.JSON(http.StatusUnauthorized, out)
			}
			if claims == nil {
				out := models.BaseResponse{Msg: fmt.Sprintf("token not found.%s,%s,%v", NotAuthPath[c.Path()], c.Path(), NotAuthPath)}
				return c.JSON(http.StatusUnauthorized, out)
			}

			diagnoseGrpcTokenInfo := &models.MemberInfo{
				ScrmUserId:     cast.ToString(claims["scrmid"]),
				ScrmUserName:   cast.ToString(claims["nanmeid"]),
				ScrmUserMobile: cast.ToString(claims["mobile"]),
			}
			c.Set("member_info", diagnoseGrpcTokenInfo)
			c.Set("channel_id", cast.ToInt32(c.Request().Header.Get("channel_id")))
			c.Set("user_agent", cast.ToInt32(c.Request().Header.Get("user_agent")))
			c.Set("open_id", cast.ToString(c.Request().Header.Get("open_id")))
			c.Set("user_id", cast.ToString(claims["userId"]))
			return next(c)
		}
	}
}
