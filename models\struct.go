package models

import (
	"encoding/json"
)

type (
	BaseResponse struct {
		//返回提示信息
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	MemberInfo struct {
		Id        string `json:"id"`
		Name      string `json:"name"`
		CacheTime int64  `json:"cache_time"`

		ScrmUserId     string `json:"scrm_user_id"`
		ScrmUserName   string `json:"scrm_user_name"`
		ScrmUserMobile string `json:"scrm_user_mobile"`
	}

	/*************************** bj scrm api end ***************************/
)

func (s *MemberInfo) MarshalBinary() ([]byte, error) {
	return json.Marshal(s)
}

func (s *MemberInfo) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, s)
}
