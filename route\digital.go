package route

import (
	"_/controller"
	"github.com/labstack/echo/v4"
)

//数字藏品 相关路由
func DigitalRoute(g *echo.Group) {
	//数字藏品接口
	g = g.Group("/digital")

	// 用户实名注册接口
	g.POST("/register", controller.UserRegister)

	// 数字藏品素材上传
	g.GET("/upload", controller.DigitalImageUpload)

	g.POST("/nft_claim", controller.NftClaim)

	// 发行数字藏品
	g.POST("/nft_publish", controller.NftPublish)

	// 用户数字藏品列表
	g.GET("/user_nft_list", controller.UserNftList)

	// 查询任务结果
	g.GET("/nft_result", controller.NftResult)

	// 积分查询
	g.GET("/nft_point_query", controller.NftPointQuery)
	//数字藏品详细信息
	g.GET("/nft_info", controller.NftInfo)
	//购买数字藏品(需申请积分)
	//g.POST("/nft_buy", controller.NftBuy)
	//变更数字藏品销售状态
	g.POST("/nft_status", controller.NftStatus)

	//创建nft存储数据库
	g.POST("/nft_create", controller.NftCreate)
	//更新数字藏品
	g.POST("/nft_update", controller.NftUpdate)
	//删除数字藏品
	g.DELETE("/nft_del/:id", controller.NftDelete)
	//数字藏品列表展示
	g.GET("/nft_flat_list", controller.NftFlatList)
	g.GET("/nft_flat_detail", controller.NftFlatDetail)

	//获取access_code
	g.GET("/user_code", controller.AccessCode)
	g.GET("/auth_openid", controller.UserAuthOpenid)

	//nft下单购买数字藏品
	g.POST("/nft_buy", controller.NftBuy)
	g.POST("/nft_pay", controller.NftPay)
}
