package controller

import (
	"_/dto"
	"_/proto/cc"
	"_/proto/ctc"
	"_/proto/dac"
	"_/utils"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"net/http"
)

// ArticleMiniFilter
// @summary 小程序文章列表过滤数据
// @tags 内容中心
// @Accept json
// @Produce json
// @success 200 {object} ctc.ArticleMiniFilterResponse
// @failure 400 {object} ctc.ArticleMiniFilterResponse
// @router /content-api/article/mini-filter [GET]
func ArticleMiniFilter(c echo.Context) error {
	out := new(ctc.ArticleMiniFilterResponse)

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	gRes, err := client.RPC.ArticleMiniFilter(client.Ctx, &ctc.EmptyRequest{})
	if err != nil {
		out.Message = err.Error()
		out.Code = 400
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	return c.JSON(http.StatusOK, gRes)
}

// ArticleList
// @summary 文章列表
// @tags 内容中心
// @Accept x-www-form-urlencoded
// @Produce json
// @param category_first body integer false "一级分类id"
// @param category_second body integer false "二级分类id"
// @param articleType body integer false "0全部 1图文 2视频"
// @param randNum body integer false "随机值，0为不随机取值"
// @param disChannel body integer false "分发渠道，1-百度小程序,2-阿闻小程序,4-阿闻APP，多渠道1+2+4，查询位运算"
// @param tags body string false "标签过滤 json字符串 [{'name':'年龄','tags':'幼年'}]"
// @param page_index body integer true "页码，默认1"
// @param page_size body integer true "每页条数，默认20"
// @success 200 {object} ctc.ArticleListResponse
// @failure 400 {object} ctc.ArticleListResponse
// @router /content-api/article/list [GET]
func ArticleList(c echo.Context) error {
	out := new(ctc.ArticleListMiniResponse)
	in := new(ctc.ArticleListRequest)

	// 参数绑定
	in.PageIndex = cast.ToInt32(c.FormValue("page_index"))
	in.PageSize = cast.ToInt32(c.FormValue("page_size"))
	in.ArticleType = cast.ToInt32(c.FormValue("articleType"))
	in.CategoryFirst = cast.ToInt32(c.FormValue("category_first"))
	in.CategorySecond = cast.ToInt32(c.FormValue("category_second"))
	in.Tags = c.FormValue("tags")
	in.DisChannel = cast.ToInt32(c.FormValue("disChannel"))

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	in.OrgId = cast.ToInt32(orgId)

	if in.PageIndex < 1 || in.PageSize < 1 {
		out.Message = "page或size参数错误"
		return c.JSON(http.StatusBadRequest, out)
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	gRes, err := client.RPC.ArticleListMini(client.Ctx, in)
	if err != nil {
		out.Message = err.Error()
		out.Code = 400
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	return c.JSON(http.StatusOK, gRes)
}

// ArticleTips
// @summary 文章小贴士
// @tags 内容中心
// @Accept x-www-form-urlencoded
// @Produce json
// @success 200 {object} cc.ArticleTipsResponse
// @failure 400 {object} cc.ArticleTipsResponse
// @router /content-api/article/tips [GET]
func ArticleTips(c echo.Context) error {
	out := new(cc.ArticleTipsResponse)
	in := new(cc.ArticleTipsRequest)

	// rpc请求
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	gRes, err := client.PetTips.ArticleTips(client.Ctx, in)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	return c.JSON(http.StatusOK, gRes)
}

// ArticleDetail
// @summary 文章详情
// @tags 内容中心
// @Accept x-www-form-urlencoded
// @Produce json
// @param article_id body integer true "文章id"
// @success 200 {object} dto.ArticleDetailDto
// @failure 400 {object} dto.ArticleDetailDto
// @router /content-api/article/detail [GET]
func ArticleDetail(c echo.Context) error {
	out := new(dto.ArticleDetailDto)
	in := new(ctc.ArticleDetailRequest)

	// 参数验证
	in.ArticleId = cast.ToInt32(c.FormValue("article_id"))
	if in.ArticleId < 1 {
		out.Message = "文章id错误"
		return c.JSON(http.StatusBadRequest, out)
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	gRes, err := client.RPC.ArticleDetail(client.Ctx, in)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	if err = utils.MapTo(gRes.Detail, &out.Detail); err != nil {
		out.Code = 400
		out.Message = "查询数据失败"
	}

	if out.Detail.IsShowAds == 1 {
		dataClient := dac.GetDataCenterClient()

		if res, err := dataClient.RPC.GetAdGoodsDetail(client.Ctx, &dac.GetAdGoodsDetailReq{
			Type: 1,
		}); err == nil {
			if res.AdGoodsDetail.Status == 1 {
				out.Detail.Advertisement.Image = res.AdGoodsDetail.Image
				out.Detail.Advertisement.Path = res.AdGoodsDetail.Path
			} else {
				out.Detail.IsShowAds = 0
			}
		}
	}

	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// ArticleRecommend
// @summary 延伸阅读
// @tags 内容中心
// @Accept x-www-form-urlencoded
// @Produce json
// @param article_id body integer true "文章id"
// @success 200 {object} ctc.ArticleRecommendResponse
// @failure 400 {object} ctc.ArticleRecommendResponse
// @router /content-api/article/recommend [GET]
func ArticleRecommend(c echo.Context) error {
	in := new(ctc.ArticleRecommendRequest)

	articleId := cast.ToInt64(c.QueryParam("article_id"))
	if articleId < 1 {
		out := new(ctc.ArticleRecommendResponse)
		out.Code = 400
		out.Message = "文章id错误"
		return c.JSON(http.StatusBadRequest, out)
	}
	in.ArticleId = articleId

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	ret, err := client.RPC.ArticleRecommend(client.Ctx, in)
	if err != nil {
		ret.Message = fmt.Sprintf("ArticleRecommend调用失败 <%v>", err)
		return c.JSON(400, ret)
	}

	return c.JSON(200, ret)
}

// AdvertisementGet
// @Summary 获取广告
// @Tags 内容中心
// @Accept json
// @Produce json
// @param type body integer true "0：默认是商详广告，1：百度勃林格"
// @Success 200 {object} dto.AdvertisementDto
// @Failure 400 {object} dto.AdvertisementDto
// @Router /content-api/advertisement/get [get]
func AdvertisementGet(c echo.Context) error {
	var modelDac dac.GetAdGoodsDetailReq
	modelDac.Type = cast.ToInt32(c.QueryParam("type"))
	client := dac.GetDataCenterClient()
	defer client.Conn.Close()

	if res, err := client.RPC.GetAdGoodsDetail(client.Ctx, &modelDac); err != nil {
		fmt.Println(err.Error())
		return c.JSON(400, err)
	} else {
		var out dto.AdvertisementDto
		out.Code = res.Code
		out.Message = res.Message

		if err := utils.MapTo(res.AdGoodsDetail, &out.Data); err != nil {
			out.Code = 400
			out.Message = "查询数据失败"
		}
		return c.JSON(int(out.Code), out)
	}
}

// CategoryBar
// @Summary 获取类目栏填充类目
// @Tags 内容中心
// @Accept x-www-form-urlencoded
// @Produce json
// @param type body integer true "0：默认一级分类，1：二级分类"
// @param tags body string false "标签过滤 json字符串 [{'name':'年龄','tags':'幼年'}]"
// @Success 200 {object} ctc.CategoryBarResponse
// @Failure 400 {object} ctc.CategoryBarResponse
// @Router /content-api/article/category_bar [get]
func CategoryBar(c echo.Context) error {
	in := new(ctc.CategoryBarRequest)

	in.Type = cast.ToInt32(c.QueryParam("type"))
	in.Tags = c.FormValue("tags") // 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	in.OrgId = cast.ToInt32(orgId)

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	ret, err := client.RPC.CategoryBar(client.Ctx, in)
	if err != nil {
		ret.Message = fmt.Sprintf("CategoryBar调用失败 <%v>", err)
		return c.JSON(400, ret)
	}

	return c.JSON(200, ret)
}

// GetStatistics
// @Summary 获取二级类目和文章的条数
// @Tags 内容中心
// @Accept x-www-form-urlencoded
// @Produce json
// @Success 200 {object} dto.GetStatisticsDto
// @Failure 400 {object} dto.GetStatisticsDto
// @Router /content-api/article/statistics [get]
func GetStatistics(c echo.Context) error {
	out := new(dto.GetStatisticsDto)
	out.Code = 400

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	res, err := client.RPC.GetStatistics(client.Ctx, &ctc.OrgIdRequest{
		OrgId: cast.ToInt32(orgId),
	})
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	out.Code = 200
	out.Data = res

	return c.JSON(200, out)
}
