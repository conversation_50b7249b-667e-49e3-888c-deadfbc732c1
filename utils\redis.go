package utils

import (
	"strconv"

	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
)

//redis链接
var RedisClient *redis.Client

func init() {
	DB, _ := strconv.Atoi(config.GetString("redis.DB"))
	RedisClient = redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Addr"),
		Password: config.GetString("redis.Password"),
		DB:       DB,
		PoolSize: 10,
	})
}

const RedisNil = redis.Nil
const RedisTxFailedErr = redis.TxFailedErr
