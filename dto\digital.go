package dto

type RespDto struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

//UserRegisterDto 用户实名注册
type UserRegisterDto struct {
	PersonName string `json:"person_name"`
	Mobile     string `json:"mobile"`
	IdCard     string `json:"id_card"`
	Openid     string `json:"openid"`
}

//NftPublishDto 数字藏品发行参数
type NftPublishDto struct {
	Author           string `json:"author"`
	Name             string `json:"name"`
	Url              string `json:"url"`
	DisplayUrl       string `json:"display_url"`
	Desc             string `json:"desc"`
	Flag             string `json:"flag"`
	PublishCount     int    `json:"publish_count"`
	SeriesId         string `json:"series_id"`
	SeriesBeginIndex int    `json:"series_begin_index"`
	SellStatus       int    `json:"sell_status"`
	SellCount        int    `json:"sell_count"`
	PackageType      string `json:"package_type"`
}

//NftClaimDto 数字藏品系列声明参数
type NftClaimDto struct {
	SeriesName string `json:"series_name"`
	TotalCount int    `json:"total_count"`
	CoverUrl   string `json:"cover_url"`
	Desc       string `json:"desc"`
}

//NftBuyDto 数字藏品购买
type NftBuyDto struct {
	PublishIdentification string  `json:"publish_identification" validate:"required"`
	SeriesId              string  `json:"series_id" validate:"required"`
	Price                 float64 `json:"price"`
	SellType              int     `json:"sell_type"`
}

//NftStatusDto 数字藏品状态变更
type NftStatusDto struct {
	NftId       string `json:"nft_id"`
	TransStatus int    `json:"trans_status"`
	TransPrice  int    `json:"trans_price"`
}

//NftCreateDto 创建数字藏品
type NftCreateDto struct {
	NftFlagDto
	UserIdentification string `json:"user_identification"`
}

//NftUpdateDto 更新数字藏品
type NftUpdateDto struct {
	Id     int `json:"id"`
	Status int `json:"status"`
	NftCreateDto
}

//NftFlagDto 数字藏品列表
type NftFlagDto struct {
	//藏品名称
	Name string `json:"name"`
	//藏品封面图
	ImageUrl string `json:"image_url"`
	//藏品描述
	Desc string `json:"desc"`
	//藏品系列
	SeriesId string `json:"series_id"`
	//藏品价格
	Price float64 `json:"price"`
	//发行人唯一标识
	PublishIdentification string `json:"publish_identification"`
	//详情图片
	Detail string `json:"detail"`
	//是否购买唯一标识,0是未购买,1是已购买
	Flag int32 `json:"flag"`
}

//NftPayDto 数字藏品支付
type NftPayDto struct {
	//数字藏品订单号
	OrderSn string `json:"order_sn" validate:"required"`
}

//NftPayResult 支付返回结果
type NftPayResult struct {
	Code    int      `json:"code"`
	Message string   `json:"message"`
	Details *Details `json:"details"`
}

//Details 支付详情参数
type Details struct {
	WxJsapi *WxJsapi `json:"wx_jsapi"`
	OrderId string   `json:"order_id"`
}

//WxJsapi js支付参数
type WxJsapi struct {
	Appid     string `json:"appId"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	PaySign   string `json:"paySign"`
	SignType  string `json:"signType"`
	TimeStamp string `json:"timeStamp"`
}
