package models

type AddVisitRecord struct {
	//文章id
	ContentId int32 `json:"content_id"`
	//类型 1：文章访问 2：视频播放
	Type int32 `json:"type"`
	// 访问渠道 1：百度小程序 2：阿闻小程序  3：安卓 4：IOS
	Channel int32 `json:"channel"`
	//用户编号或者openId
	UserNo string `json:"user_no"`
}


type AddBoehringereRecord struct {
	// 类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
	Type int32 `json:"type"`
	// 类型子级（0为标题，1为内容一，以此类推）
	TypeChild int32 `json:"type_child"`
}