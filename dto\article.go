package dto

import "_/proto/ctc"

type ArticleDetailDto struct {
	Code    int32         `json:"code"`
	Message string        `json:"message"`
	Detail  ArticleDetail `json:"detail"`
}
type ArticleDetail struct {
	// 模板id
	TemplateId int32 `json:"template_id"`
	// 一级分类id
	CategoryFirst int32 `json:"category_first"`
	// 二级分类id
	CategorySecond int32 `json:"category_second"`
	// 三级分类id
	CategoryThird int32 `json:"category_third"`
	// 视频地址
	VideoUrl string `json:"video_url"`
	// 封面地址
	CoverUrl string `json:"cover_url"`
	// 标题
	Title string `json:"title"`
	// 内容
	Content string `json:"content"`
	// 医生code，接口中doctor_code
	DoctorCode string `json:"doctor_code"`
	// 执业证书编号
	DoctorCertNo string `json:"doctor_cert_no"`
	// 标签集合
	TagJson string `json:"tag_json"`
	// 分发渠道，1-百度小程序,2-阿闻小程序，4-阿闻app
	DisChannel    int32   `json:"dis_channel"`
	DisChannelArr []int32 `json:"dis_channel_arr"`
	// 是否显示广告，1-显示，0-不显示
	IsShowAds int32 `json:"is_show_ads"`
	// 在线问诊入口图片地址
	OnlineAskUrl string `json:"online_ask_url"`
	// 文章类型：1-图文，2-视频
	ArticleType int32 `json:"article_type"`
	// 文章id
	ArticleId int32 `json:"article_id"`
	// 最后更新时间
	UpdatedAt string `json:"updated_at"`
	//医生名称
	DoctorName string `json:"doctor_name"`
	//医生称号（职级、岗位）
	DoctorLevel string `json:"doctor_level"`
	//医生头像
	DoctorImg string `json:"doctor_img"`
	//医院名称
	HospitalName  string        `json:"hospital_name"`
	Advertisement Advertisement `json:"advertisement"`
}

type GetStatisticsDto struct {
	Code    int32                      `json:"code"`
	Message string                     `json:"message"`
	Data    *ctc.GetStatisticsResponse `json:"data"`
}
