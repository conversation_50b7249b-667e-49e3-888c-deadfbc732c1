// GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag

package docs

import (
	"bytes"
	"encoding/json"
	"strings"

	"github.com/alecthomas/template"
	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{.Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "license": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/content-api/advertisement/get": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "获取广告",
                "parameters": [
                    {
                        "description": "0：默认是商详广告，1：百度勃林格",
                        "name": "type",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.AdvertisementDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.AdvertisementDto"
                        }
                    }
                }
            }
        },
        "/content-api/article/category_bar": {
            "get": {
                "consumes": [
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "获取类目栏填充类目",
                "parameters": [
                    {
                        "description": "0：默认一级分类，1：二级分类",
                        "name": "type",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "标签过滤 json字符串 [{'name':'年龄','tags':'幼年'}]",
                        "name": "tags",
                        "in": "body",
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.CategoryBarResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.CategoryBarResponse"
                        }
                    }
                }
            }
        },
        "/content-api/article/detail": {
            "get": {
                "consumes": [
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "文章详情",
                "parameters": [
                    {
                        "description": "文章id",
                        "name": "article_id",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.ArticleDetailDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.ArticleDetailDto"
                        }
                    }
                }
            }
        },
        "/content-api/article/list": {
            "get": {
                "consumes": [
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "文章列表",
                "parameters": [
                    {
                        "description": "一级分类id",
                        "name": "category_first",
                        "in": "body",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "二级分类id",
                        "name": "category_second",
                        "in": "body",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "0全部 1图文 2视频",
                        "name": "articleType",
                        "in": "body",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "随机值，0为不随机取值",
                        "name": "randNum",
                        "in": "body",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "分发渠道，1-百度小程序,2-阿闻小程序,4-阿闻APP，多渠道1+2+4，查询位运算",
                        "name": "disChannel",
                        "in": "body",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "标签过滤 json字符串 [{'name':'年龄','tags':'幼年'}]",
                        "name": "tags",
                        "in": "body",
                        "schema": {
                            "type": "string"
                        }
                    },
                    {
                        "description": "页码，默认1",
                        "name": "page_index",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "每页条数，默认20",
                        "name": "page_size",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.ArticleListResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.ArticleListResponse"
                        }
                    }
                }
            }
        },
        "/content-api/article/mini-filter": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "小程序文章列表过滤数据",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.ArticleMiniFilterResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.ArticleMiniFilterResponse"
                        }
                    }
                }
            }
        },
        "/content-api/article/recommend": {
            "get": {
                "consumes": [
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "延伸阅读",
                "parameters": [
                    {
                        "description": "文章id",
                        "name": "article_id",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "integer"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.ArticleRecommendResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.ArticleRecommendResponse"
                        }
                    }
                }
            }
        },
        "/content-api/article/statistics": {
            "get": {
                "consumes": [
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "获取二级类目和文章的条数",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.GetStatisticsDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.GetStatisticsDto"
                        }
                    }
                }
            }
        },
        "/content-api/article/tips": {
            "get": {
                "consumes": [
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "文章小贴士",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/cc.ArticleTipsResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/cc.ArticleTipsResponse"
                        }
                    }
                }
            }
        },
        "/content-api/biliad/callback": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "B站广告推广回调地址",
                "parameters": [
                    {
                        "type": "string",
                        "description": "追踪id",
                        "name": "track_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "B站账户ID",
                        "name": "account_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "B站计划ID",
                        "name": "campaign_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "B站单元ID",
                        "name": "unit_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "B站创意ID",
                        "name": "creative_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "客户端操作系统：0 Andriod、1 IOS、2 WindowsPhone、3 其他",
                        "name": "os",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户终端的IMEI的md5值",
                        "name": "imei",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "回调地址，urlencode值",
                        "name": "callback_url",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户终端的eth0接口的MAC地址,大写MAC的md5值",
                        "name": "mac1",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "iOS idfa广告标识，适用于iOS6及以上，取md5值",
                        "name": "idfa",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "中广协CAID",
                        "name": "caid",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "安卓广告标识 Android Advertising ID",
                        "name": "aaid",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户终端安卓id，取md5值",
                        "name": "android_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "安卓设备ID，取md5值",
                        "name": "oaid",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "投放系统服务器观察到的用户远程IP",
                        "name": "ip",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "数据上报终端设备的User Agent，需escape转义",
                        "name": "ua",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "手机型号，仅支持iphone",
                        "name": "model",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "客户端触发监测的时间，毫秒",
                        "name": "ts",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "店铺ID",
                        "name": "shop_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "视频UP主MID",
                        "name": "up_mid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    }
                }
            }
        },
        "/content-api/biliad/check-conv": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "校验B站广告归因",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.BiliAdConversion"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    }
                }
            }
        },
        "/content-api/census/boehringere": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "添加勃林格访问记录",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AddBoehringereRecord"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    }
                }
            }
        },
        "/content-api/census/visit": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容中心"
                ],
                "summary": "添加访问记录",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AddVisitRecord"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/ctc.ContentResponse"
                        }
                    }
                }
            }
        },
        "/content-api/digital/auth_openid": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "获取UserAuthOpenid",
                "parameters": [
                    {
                        "type": "string",
                        "description": "code码",
                        "name": "code",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    }
                }
            }
        },
        "/content-api/digital/nft_buy": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "数字藏品购买",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.NftBuyDto"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    }
                }
            }
        },
        "/content-api/digital/nft_flat_detail": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "数字藏品详情展示",
                "parameters": [
                    {
                        "type": "string",
                        "description": "系列id",
                        "name": "series_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.NftFlagDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.NftFlagDto"
                        }
                    }
                }
            }
        },
        "/content-api/digital/nft_flat_list": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "数字藏品列表展示",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/dto.NftFlagDto"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/dto.NftFlagDto"
                            }
                        }
                    }
                }
            }
        },
        "/content-api/digital/nft_pay": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "数字藏品支付",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.NftPayDto"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    }
                }
            }
        },
        "/content-api/digital/register": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "用户实名注册",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UserRegisterDto"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    }
                }
            }
        },
        "/content-api/digital/user_code": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "获取access_code链接",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    }
                }
            }
        },
        "/content-api/digital/user_nft_list": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数字藏品"
                ],
                "summary": "获取nft数字藏品列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "第几页,不传默认为1",
                        "name": "page",
                        "in": "path"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量,不传默认为10",
                        "name": "size",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dto.RespDto"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "cc.ArticleTipsData": {
            "type": "object",
            "properties": {
                "article_id": {
                    "description": "文章id",
                    "type": "integer"
                },
                "title": {
                    "description": "文章标题",
                    "type": "string"
                }
            }
        },
        "cc.ArticleTipsResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "响应码",
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/cc.ArticleTipsData"
                    }
                },
                "message": {
                    "description": "返回信息",
                    "type": "string"
                }
            }
        },
        "ctc.ArticleListData": {
            "type": "object",
            "properties": {
                "article_id": {
                    "description": "文章id",
                    "type": "integer"
                },
                "article_type": {
                    "description": "文章分类，1-图文，2-视频",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "doctor_name": {
                    "description": "医生姓名",
                    "type": "string"
                },
                "last_operator": {
                    "description": "最后操作人",
                    "type": "string"
                },
                "last_publish_time": {
                    "description": "最后发布时间",
                    "type": "string"
                },
                "status": {
                    "description": "文章状态，0-未发布，1-已发布，2-下架",
                    "type": "string"
                },
                "title": {
                    "description": "标题",
                    "type": "string"
                },
                "updated_at": {
                    "description": "最后更新时间",
                    "type": "string"
                }
            }
        },
        "ctc.ArticleListResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "响应码",
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ctc.ArticleListData"
                    }
                },
                "message": {
                    "description": "返回信息",
                    "type": "string"
                },
                "total_count": {
                    "type": "integer"
                }
            }
        },
        "ctc.ArticleMiniFilterResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "200正常，400错误",
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "tag_groups": {
                    "description": "标签组数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ctc.ArticleTagGroup"
                    }
                }
            }
        },
        "ctc.ArticleRecommend": {
            "type": "object",
            "properties": {
                "article_type": {
                    "description": "文章类型 1-图文，2-视频",
                    "type": "integer"
                },
                "certificate": {
                    "description": "证书",
                    "type": "string"
                },
                "cover": {
                    "description": "文章封面",
                    "type": "string"
                },
                "doctor_code": {
                    "description": "医生编码",
                    "type": "string"
                },
                "doctor_img": {
                    "description": "医生头像",
                    "type": "string"
                },
                "doctor_level": {
                    "description": "医生职称",
                    "type": "string"
                },
                "doctor_name": {
                    "description": "医生名称",
                    "type": "string"
                },
                "hospital_name": {
                    "description": "医院名称",
                    "type": "string"
                },
                "id": {
                    "description": "文章id",
                    "type": "integer"
                },
                "title": {
                    "description": "文章标题",
                    "type": "string"
                }
            }
        },
        "ctc.ArticleRecommendResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ctc.ArticleRecommend"
                    }
                },
                "message": {
                    "type": "string"
                },
                "total_count": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "ctc.ArticleTagGroup": {
            "type": "object",
            "properties": {
                "name": {
                    "description": "标签组名称",
                    "type": "string"
                },
                "tags": {
                    "description": "多个标签，用逗号分割",
                    "type": "string"
                }
            }
        },
        "ctc.Category": {
            "type": "object",
            "properties": {
                "category_name": {
                    "type": "string"
                },
                "create_id": {
                    "type": "string"
                },
                "create_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "level": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "valid": {
                    "type": "integer"
                }
            }
        },
        "ctc.CategoryBarResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ctc.Category"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "ctc.ContentResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "ctc.GetStatisticsResponse": {
            "type": "object",
            "properties": {
                "articleCount": {
                    "description": "文章条数",
                    "type": "integer"
                },
                "categoryCount": {
                    "description": "类目条数",
                    "type": "integer"
                }
            }
        },
        "dto.Advertisement": {
            "type": "object",
            "properties": {
                "image": {
                    "description": "图片",
                    "type": "string"
                },
                "path": {
                    "description": "路径",
                    "type": "string"
                }
            }
        },
        "dto.AdvertisementDto": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "object",
                    "$ref": "#/definitions/dto.Advertisement"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dto.ArticleDetail": {
            "type": "object",
            "properties": {
                "advertisement": {
                    "type": "object",
                    "$ref": "#/definitions/dto.Advertisement"
                },
                "article_id": {
                    "description": "文章id",
                    "type": "integer"
                },
                "article_type": {
                    "description": "文章类型：1-图文，2-视频",
                    "type": "integer"
                },
                "category_first": {
                    "description": "一级分类id",
                    "type": "integer"
                },
                "category_second": {
                    "description": "二级分类id",
                    "type": "integer"
                },
                "category_third": {
                    "description": "三级分类id",
                    "type": "integer"
                },
                "content": {
                    "description": "内容",
                    "type": "string"
                },
                "cover_url": {
                    "description": "封面地址",
                    "type": "string"
                },
                "dis_channel": {
                    "description": "分发渠道，1-百度小程序,2-阿闻小程序，4-阿闻app",
                    "type": "integer"
                },
                "dis_channel_arr": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "doctor_cert_no": {
                    "description": "执业证书编号",
                    "type": "string"
                },
                "doctor_code": {
                    "description": "医生code，接口中doctor_code",
                    "type": "string"
                },
                "doctor_img": {
                    "description": "医生头像",
                    "type": "string"
                },
                "doctor_level": {
                    "description": "医生称号（职级、岗位）",
                    "type": "string"
                },
                "doctor_name": {
                    "description": "医生名称",
                    "type": "string"
                },
                "hospital_name": {
                    "description": "医院名称",
                    "type": "string"
                },
                "is_show_ads": {
                    "description": "是否显示广告，1-显示，0-不显示",
                    "type": "integer"
                },
                "online_ask_url": {
                    "description": "在线问诊入口图片地址",
                    "type": "string"
                },
                "tag_json": {
                    "description": "标签集合",
                    "type": "string"
                },
                "template_id": {
                    "description": "模板id",
                    "type": "integer"
                },
                "title": {
                    "description": "标题",
                    "type": "string"
                },
                "updated_at": {
                    "description": "最后更新时间",
                    "type": "string"
                },
                "video_url": {
                    "description": "视频地址",
                    "type": "string"
                }
            }
        },
        "dto.ArticleDetailDto": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "detail": {
                    "type": "object",
                    "$ref": "#/definitions/dto.ArticleDetail"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dto.GetStatisticsDto": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "object",
                    "$ref": "#/definitions/ctc.GetStatisticsResponse"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dto.NftBuyDto": {
            "type": "object",
            "required": [
                "publish_identification",
                "series_id"
            ],
            "properties": {
                "price": {
                    "type": "number"
                },
                "publish_identification": {
                    "type": "string"
                },
                "sell_type": {
                    "type": "integer"
                },
                "series_id": {
                    "type": "string"
                }
            }
        },
        "dto.NftFlagDto": {
            "type": "object",
            "properties": {
                "desc": {
                    "description": "藏品描述",
                    "type": "string"
                },
                "detail": {
                    "description": "详情图片",
                    "type": "string"
                },
                "flag": {
                    "description": "是否购买唯一标识,0是未购买,1是已购买",
                    "type": "integer"
                },
                "image_url": {
                    "description": "藏品封面图",
                    "type": "string"
                },
                "name": {
                    "description": "藏品名称",
                    "type": "string"
                },
                "price": {
                    "description": "藏品价格",
                    "type": "number"
                },
                "publish_identification": {
                    "description": "发行人唯一标识",
                    "type": "string"
                },
                "series_id": {
                    "description": "藏品系列",
                    "type": "string"
                }
            }
        },
        "dto.NftPayDto": {
            "type": "object",
            "required": [
                "order_sn"
            ],
            "properties": {
                "order_sn": {
                    "description": "数字藏品订单号",
                    "type": "string"
                }
            }
        },
        "dto.RespDto": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "object"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dto.UserRegisterDto": {
            "type": "object",
            "properties": {
                "id_card": {
                    "type": "string"
                },
                "mobile": {
                    "type": "string"
                },
                "openid": {
                    "type": "string"
                },
                "person_name": {
                    "type": "string"
                }
            }
        },
        "models.AddBoehringereRecord": {
            "type": "object",
            "properties": {
                "type": {
                    "description": "类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四",
                    "type": "integer"
                },
                "type_child": {
                    "description": "类型子级（0为标题，1为内容一，以此类推）",
                    "type": "integer"
                }
            }
        },
        "models.AddVisitRecord": {
            "type": "object",
            "properties": {
                "channel": {
                    "description": "访问渠道 1：百度小程序 2：阿闻小程序  3：安卓 4：IOS",
                    "type": "integer"
                },
                "content_id": {
                    "description": "文章id",
                    "type": "integer"
                },
                "type": {
                    "description": "类型 1：文章访问 2：视频播放",
                    "type": "integer"
                },
                "user_no": {
                    "description": "用户编号或者openId",
                    "type": "string"
                }
            }
        },
        "models.BiliAdConversion": {
            "type": "object",
            "properties": {
                "client_ip": {
                    "description": "设备的联网公网IP",
                    "type": "string"
                },
                "conv_type": {
                    "description": "转化类型，APP_FIRST_ACTIVE：新用户下载应用后首次打开应用",
                    "type": "string"
                },
                "idfa": {
                    "description": "iOS广告标识",
                    "type": "string"
                },
                "imei": {
                    "description": "安卓用户终端的IMEI",
                    "type": "string"
                },
                "mac": {
                    "description": "用户终端的eth0接口的MAC地址",
                    "type": "string"
                },
                "model": {
                    "description": "用户手机型号",
                    "type": "string"
                },
                "nonce": {
                    "description": "不重复的随机数",
                    "type": "string"
                },
                "oaid": {
                    "description": "安卓终端设备ID",
                    "type": "string"
                },
                "os": {
                    "description": "客户端操作系统：0 Andriod、1 IOS、2 WindowsPhone、3 其他",
                    "type": "integer"
                },
                "sign": {
                    "description": "请求签名，md5(arg1Value1arg2value2...)",
                    "type": "string"
                },
                "timestamp": {
                    "description": "请求时间戳",
                    "type": "integer"
                },
                "ua": {
                    "description": "数据上报终端设备的User Agent",
                    "type": "string"
                }
            }
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "1.0",
	Host:        "localhost:7086",
	BasePath:    "",
	Schemes:     []string{},
	Title:       "项目接口文档",
	Description: "这里是描述",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register(swag.Name, &s{})
}
