package route

import (
	myMiddleware "_/middleware"

	"_/controller"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	echoSwagger "github.com/swaggo/echo-swagger"
	r "github.com/tricobbler/echo-tool/httpError"
	rpMiddleware "github.com/tricobbler/echo-tool/middleware"
	"github.com/tricobbler/echo-tool/validate"
)

func InitRoute() *echo.Echo {
	e := echo.New()

	//添加swagger文档地址
	e.GET("/swagger/*", echoSwagger.WrapHandler)

	//错误中间件
	e.Use(rpMiddleware.MyRecover(middleware.RecoverConfig{
		Skipper:           middleware.DefaultSkipper,
		StackSize:         4 << 10, // 4 KB
		DisableStackAll:   false,
		DisablePrintStack: false,
	}))

	//校验渠道id和来源，并写入context
	e.Use(myMiddleware.CheckChannel())

	//返回错误信息处理，屏蔽内部服务调用错误
	e.Use(rpMiddleware.MyErrorHandle())

	//设置自定义错误响应
	e.HTTPErrorHandler = r.HttpErrorHandler

	//自定义参数验证插件
	e.Validator = validate.NewCustomValidator()

	return Route(e)
}

//路由
func Route(e *echo.Echo) *echo.Echo {
	g := e.Group("content-api")

	//广告
	AdvertisementRoute(g)

	//文章
	ArticleRoute(g)

	//统计
	CensusRoute(g)

	// B站广告推广统计
	BiliAdRoute(g)

	//数字藏品
	DigitalRoute(g)
	
	return e
}

func AdvertisementRoute(g *echo.Group) {
	g = g.Group("/advertisement")

	// 获取广告
	g.GET("/get", controller.AdvertisementGet)
}
func CensusRoute(g *echo.Group) {
	g = g.Group("/census")

	// 添加访问记录
	g.POST("/visit", controller.AddVisitRecord)

	// 添加勃林格访问记录
	g.POST("/boehringere", controller.AddBoehringereRecord)

}

func BiliAdRoute(g *echo.Group) {
	g = g.Group("/biliad")

	// B站广告点击回调
	g.GET("/callback", controller.BiliAdCallback)
	// 校验归因
	g.POST("/check-conv", controller.CheckBiliAdConversion)

}
