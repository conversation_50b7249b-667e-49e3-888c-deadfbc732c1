package route

import (
	"_/controller"
	"github.com/labstack/echo/v4"
)

//文章模块 相关路由
func ArticleRoute(g *echo.Group) {
	//文章接口
	g = g.Group("/article")

	// 文章列表
	g.GET("/list", controller.ArticleList)
	// 小程序文章列表过滤数据
	g.GET("/mini-filter", controller.ArticleMiniFilter)
	// 文章小贴士
	g.GET("/tips", controller.ArticleTips)
	// 文章详情
	g.GET("/detail", controller.ArticleDetail)
	// 延伸阅读
	g.GET("/recommend", controller.ArticleRecommend)
	// 获取一级类目
	g.GET("/category_bar", controller.CategoryBar)
	//获取文章类目条数
	g.GET("/statistics", controller.GetStatistics)
}
