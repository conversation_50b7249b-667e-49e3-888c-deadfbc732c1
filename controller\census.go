package controller

import (
	"_/models"
	"_/proto/ctc"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

//AddVisitRecord
// @Summary 添加访问记录
// @Tags 内容中心
// @Accept json
// @Produce json
// @Param model body models.AddVisitRecord true " "
// @Success 200 {object} ctc.ContentResponse
// @Failure 400 {object} ctc.ContentResponse
// @Router /content-api/census/visit [POST]
func AddVisitRecord(c echo.Context) error {
	model := new(models.AddVisitRecord)
	if err := c.Bind(model); err != nil {
		glog.Info("添加访问记录:获取返回参数失败" + err.Error())
		return c.JSON(200, ctc.ContentResponse{Code: 400,Message: "参数错误"})
	}

	if len(model.UserNo)<=0{
		userInfo,_ := utils.GetPayloadDirectlyToInterface(c)
		model.UserNo = userInfo.ObjectId
	}

	client := ctc.GetContentCenterClient()
	defer client.Close()

	if out, err := client.CensusRPC.AddVisitRecord(client.Ctx, &ctc.AddVisitRecordRequest{
		ContentId:            model.ContentId,
		Type:                 model.Type,
		Channel:              model.Channel,
		UserNo:               model.UserNo,
		Ip:                   c.RealIP(),
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}
//AddBoehringereRecord
// @Summary 添加勃林格访问记录
// @Tags 内容中心
// @Accept json
// @Produce json
// @Param model body models.AddBoehringereRecord true " "
// @Success 200 {object} ctc.ContentResponse
// @Failure 400 {object} ctc.ContentResponse
// @Router /content-api/census/boehringere [POST]
func AddBoehringereRecord(c echo.Context) error {
	model := new(models.AddBoehringereRecord)
	if err := c.Bind(model); err != nil {
		glog.Info("添加访问记录:获取返回参数失败" + err.Error())
		return c.JSON(200, ctc.ContentResponse{Code: 400,Message: "参数错误"})
	}
	//userInfo,_ := utils.GetPayloadDirectlyToInterface(c)

	client := ctc.GetContentCenterClient()
	defer client.Close()


	if out, err := client.CensusRPC.AddBoehringereChickRecord(client.Ctx, &ctc.BoehringereChickRecord{
		Type:                 model.Type,
		TypeChild:            model.TypeChild,
		Ip:                   c.RealIP(),
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}
